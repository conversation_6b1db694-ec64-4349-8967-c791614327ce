package com.chinaservices.core.handler;

import com.chinaservices.kernel.exception.GlobalExceptionHandler;
import com.chinaservices.sdk.support.result.ResponseData;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.async.AsyncRequestTimeoutException;

/**
 * <AUTHOR>
 */
@RestControllerAdvice
@Slf4j
public class CustomGlobalExceptionHandler extends GlobalExceptionHandler {

    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler({Exception.class})
    public ResponseData exception(Exception e, HttpServletRequest request) {
        if ("Broken pipe".equalsIgnoreCase(e.getMessage()) ||
                "Connection reset by peer".equalsIgnoreCase(e.getMessage())) {
            // 客户端断开连接，不记录堆栈
            log.debug("客户端连接断开: {}", e.getMessage());
            return null;
        }
        log.error("", e);
        return super.exception(e, request);
    }

    /**
     * 忽略异常
     * @param e
     * @param request
     * @return
     */
    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler({AsyncRequestTimeoutException.class})
    public ResponseData throwable(Throwable e, HttpServletRequest request) {
        return null;
    }
}

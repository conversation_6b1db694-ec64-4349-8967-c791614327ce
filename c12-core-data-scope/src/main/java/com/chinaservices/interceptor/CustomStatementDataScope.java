package com.chinaservices.interceptor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.chinaservices.annotation.DataPermission;
import com.chinaservices.auth.PositionDTO;
import com.chinaservices.context.DataPermissionContext;
import com.chinaservices.feign.RemotePositionWarehouseFeign;
import com.chinaservices.sdk.support.result.ResponseData;
import com.chinaservices.session.SessionContext;
import com.chinaservices.util.StringPool;
import com.chinaservices.util.StringUtil;
import com.chinaservices.utils.SqlUtils;
import com.google.common.collect.Maps;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.schema.Table;

import java.util.List;
import java.util.Map;

import static com.chinaservices.auth.DataScopeConstant.*;

@Slf4j
public class CustomStatementDataScope {

    public static final Map<String, Object> TABLE_CLASS_MAP = Maps.newConcurrentMap();

    public static final Map<String, List<String>> COLUMN_CLASS_MAP = Maps.newConcurrentMap();

    private static final String USER_SQL_ID = StringPool.LEFT_BRACE + StringPool.RIGHT_BRACE + StringPool.EQUALS + StringPool.LEFT_BRACE + StringPool.RIGHT_BRACE;

    private static final String WAREHOUSE_SQL_ID = StringPool.LEFT_BRACE + StringPool.RIGHT_BRACE + IN + StringPool.LEFT_BRACKET + StringPool.LEFT_BRACE + StringPool.RIGHT_BRACE + StringPool.RIGHT_BRACKET;


    @SneakyThrows
    public static Expression compositeSelect(Table table) {
        String userSql = StrUtil.format(USER_SQL_ID, USER_ID_COL);
        String warehouseSql = StringUtil.format(WAREHOUSE_SQL_ID, WAREHOUSE_COL);
        if (MapUtil.getBool(TABLE_CLASS_MAP, SqlUtils.normalizeWithCache(table.getName()))) {
            log.info("数据权限，排除表名：{}", table.getName());
            return null;
        }
        if (DataPermissionContext.get() != null && !DataPermissionContext.get().enable()) {
            return null;
        }
        String positionStr = Convert.toStr(SessionContext.get().getAttributes().get(POST_KEY), StringPool.EMPTY);
        RemotePositionWarehouseFeign remotePositionWarehouseFeign = SpringUtil.getBean(RemotePositionWarehouseFeign.class);
        ResponseData<String> positionWarehouseResponse = remotePositionWarehouseFeign.getPositionWarehouse(SessionContext.get());
        if (positionWarehouseResponse.getSuccess()) {
            positionStr = positionWarehouseResponse.getData();
        }
        if (StringPool.EMPTY.equals(positionStr)){
            return null;
        }
        PositionDTO positionDTO = JSONUtil.toBean(positionStr, PositionDTO.class);
        if (CollUtil.isEmpty(positionDTO.getWarehouseIds())) {
            log.info("权限配置错误，查询配置的仓库ID为空");
            return null;
        }
        if (ObjectUtil.isNotNull(positionDTO) && positionDTO.isAdmin()) {
            return null;
        }
        String tableName = SqlUtils.normalizeWithCache(table.getName());
        String warehouseField = WAREHOUSE_ID_D;
        boolean user = true;
        if (DataPermissionContext.get() != null && DataPermissionContext.get().enable()) {
            DataPermission dataPermission = DataPermissionContext.get();
            userSql = StrUtil.format(USER_SQL_ID, dataPermission.userField());
            warehouseSql = StringUtil.format(WAREHOUSE_SQL_ID, dataPermission.warehouseField());
            warehouseField = StrUtil.toCamelCase(dataPermission.warehouseField());
            user = dataPermission.user();
        }
        if (!COLUMN_CLASS_MAP.get(tableName).contains(warehouseField)) {
            log.info("配置了数据权限，表未找到字段:{}，忽略权限，表名：{}",warehouseField, tableName);
            return null;
        }
        StringBuilder column = new StringBuilder();
        String alias = table.getName() + StringPool.DOT;
        if (ObjectUtil.isNotNull(table.getAlias())) {
            alias = table.getAlias().getName() + StringPool.DOT;
        }
        if (ObjectUtil.isNull(positionDTO)) {
            log.error("未配置数据权限！查询将设置id为0的当前用户数据");
            //设置查询空数据
            column.append(StrUtil.format(userSql, DEFAULT_ID));
            return CCJSqlParserUtil.parseCondExpression(column.toString());
        }
        List<Long> warehouseIds = positionDTO.getWarehouseIds();
        warehouseIds.add(-1L);
        column.append(StrUtil.format(alias + warehouseSql, CollUtil.join(warehouseIds, StringPool.COMMA)));
//        column.append(StrUtil.format(alias + warehouseSql, CollUtil.join(positionDTO.getWarehouseIds(), StringPool.COMMA)));
        if (positionDTO.getOrganizationPermission().equals(DATA_SCOPE_USER) && user) {
            column.append(AND);
            column.append(StrUtil.format(alias + userSql, positionDTO.getUserId()));
        }
        return CCJSqlParserUtil.parseCondExpression(column.toString());
    }
}

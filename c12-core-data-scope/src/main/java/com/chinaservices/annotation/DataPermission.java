package com.chinaservices.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import static com.chinaservices.auth.DataScopeConstant.USER_ID_COL;
import static com.chinaservices.auth.DataScopeConstant.WAREHOUSE_COL;

/**
 * 数据权限注解。
 * 注解使用在方法上。
 * - 如果不需要数据权限，可以不加注解，也可以使用 @DataPermission(enabled = false)  这个优先级低于  com.chinaservices.annotation.DataScopeIgnore
 * - 注解在接口上或者dao方法上，如果同一个方法调用多个，最后一个会覆盖前面的所有。该注解优先级低于 # com.chinaservices.annotation.DataScopeIgnore
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface DataPermission {

    /**
     * 开关 默认开
     * 优先级低于 com.chinaservices.annotation.DataScopeIgnore ，如果 添加 DataScopeIgnore，这里配置true将不生效
     */
    boolean enable() default true;

    /**
     * 仓库ID字段
     */
    String warehouseField() default WAREHOUSE_COL;

    /**
     * 用户字段
     */
    String userField() default USER_ID_COL;

    boolean user() default true;
}

<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.3.6</version>
    </parent>

    <groupId>com.chinaservices.wms</groupId>
    <artifactId>c12-core</artifactId>
    <version>1.0.2-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>c12-core</name>

    <modules>
        <module>c12-core-log</module>
        <module>c12-core-app</module>
        <module>c12-core-base</module>
        <module>c12-core-test</module>
        <module>c12-core-jpa</module>
        <module>c12-core-web</module>
        <module>c12-core-cache</module>
        <module>c12-core-utils</module>
        <module>c12-core-jackson</module>
        <module>c12-core-file</module>
        <module>c12-core-auth</module>
        <module>c12-web-starter</module>
        <module>c12-core-api</module>
        <module>c12-core-jasypt</module>
        <module>c12-core-data-scope</module>
        <module>c12-core-es</module>
        <module>c12-core-event</module>
        <module>c12-core-third</module>
        <module>c12-core-job</module>
        <module>c12-core-balancer</module>
        <module>c12-core-map</module>
        <module>c12-core-sse</module>
    </modules>

    <properties>
        <c12-core.version>1.0.2-SNAPSHOT</c12-core.version>
        <c12-module.version>1.8.6-SNAPSHOT</c12-module.version>
        <java.version>21</java.version>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.compiler.target>${java.version}</maven.compiler.target>
        <easyexcel.version>3.3.2</easyexcel.version>
        <poi.version>4.1.2</poi.version>
        <core.version>3.4.1</core.version>
        <p6spy.version>3.9.1</p6spy.version>
        <junit-jupiter.version>5.10.5</junit-jupiter.version>
        <javase.version>3.4.1</javase.version>
        <c12-export-core.version>0.1.3-SNAPSHOT</c12-export-core.version>
        <jasypt.version>3.0.5</jasypt.version>
        <spring-boot.version>3.3.6</spring-boot.version>
        <easy.version>2.0.0</easy.version>
        <es.version>7.14.0</es.version>
        <lucene.version>8.9.0</lucene.version>
        <xxl-job.version>2.4.0</xxl-job.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.chinaservices</groupId>
                <artifactId>c12-module</artifactId>
                <version>${c12-module.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.chinaservices</groupId>
                <artifactId>c12-module-api</artifactId>
                <version>${c12-module.version}</version>
            </dependency>
            <dependency>
                <groupId>com.chinaservices</groupId>
                <artifactId>c12-module-biz</artifactId>
                <version>${c12-module.version}</version>
            </dependency>
            <dependency>
                <groupId>com.chinaservices.wms</groupId>
                <artifactId>c12-core-jasypt</artifactId>
                <version>${c12-core.version}</version>
            </dependency>
            <dependency>
                <groupId>com.chinaservices.wms</groupId>
                <artifactId>c12-core-data-scope</artifactId>
                <version>${c12-core.version}</version>
            </dependency>
            <dependency>
                <groupId>com.chinaservices.wms</groupId>
                <artifactId>c12-core-web</artifactId>
                <version>${c12-core.version}</version>
            </dependency>
            <dependency>
                <groupId>com.chinaservices.wms</groupId>
                <artifactId>c12-core-event</artifactId>
                <version>${c12-core.version}</version>
            </dependency>
            <dependency>
                <groupId>com.chinaservices.wms</groupId>
                <artifactId>c12-core-app</artifactId>
                <version>${c12-core.version}</version>
            </dependency>
            <dependency>
                <groupId>com.chinaservices.wms</groupId>
                <artifactId>c12-core-base</artifactId>
                <version>${c12-core.version}</version>
            </dependency>
            <dependency>
                <groupId>com.chinaservices.wms</groupId>
                <artifactId>c12-core-cache</artifactId>
                <version>${c12-core.version}</version>
            </dependency>
            <dependency>
                <groupId>com.chinaservices.wms</groupId>
                <artifactId>c12-core-jackson</artifactId>
                <version>${c12-core.version}</version>
            </dependency>
            <dependency>
                <groupId>com.chinaservices.wms</groupId>
                <artifactId>c12-core-jpa</artifactId>
                <version>${c12-core.version}</version>
            </dependency>
            <dependency>
                <groupId>com.chinaservices.wms</groupId>
                <artifactId>c12-core-es</artifactId>
                <version>${c12-core.version}</version>
            </dependency>
            <dependency>
                <groupId>com.chinaservices.wms</groupId>
                <artifactId>c12-core-third</artifactId>
                <version>${c12-core.version}</version>
            </dependency>
            <dependency>
                <groupId>org.dromara.easy-es</groupId>
                <artifactId>easy-es-boot-starter</artifactId>
                <version>${easy.version}</version>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch.client</groupId>
                <artifactId>elasticsearch-rest-high-level-client</artifactId>
                <version>${es.version}</version>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch</groupId>
                <artifactId>elasticsearch-x-content</artifactId>
                <version>${es.version}</version>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch</groupId>
                <artifactId>elasticsearch-core</artifactId>
                <version>${es.version}</version>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch.client</groupId>
                <artifactId>elasticsearch-rest-client</artifactId>
                <version>${es.version}</version>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch</groupId>
                <artifactId>elasticsearch</artifactId>
                <version>${es.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.lucene</groupId>
                <artifactId>lucene-core</artifactId>
                <version>${lucene.version}</version>
            </dependency>
            <dependency>
                <groupId>com.chinaservices.wms</groupId>
                <artifactId>c12-core-log</artifactId>
                <version>${c12-core.version}</version>
            </dependency>
            <dependency>
                <groupId>com.chinaservices.wms</groupId>
                <artifactId>c12-core-test</artifactId>
                <version>${c12-core.version}</version>
            </dependency>
            <dependency>
                <groupId>com.chinaservices.wms</groupId>
                <artifactId>c12-core-api</artifactId>
                <version>${c12-core.version}</version>
            </dependency>
            <dependency>
                <groupId>com.chinaservices.wms</groupId>
                <artifactId>c12-core-utils</artifactId>
                <version>${c12-core.version}</version>
            </dependency>
            <dependency>
                <groupId>com.chinaservices.wms</groupId>
                <artifactId>c12-core-file</artifactId>
                <version>${c12-core.version}</version>
            </dependency>
            <dependency>
                <groupId>com.chinaservices.wms</groupId>
                <artifactId>c12-core-balancer</artifactId>
                <version>${c12-core.version}</version>
            </dependency>
            <dependency>
                <groupId>com.chinaservices.wms</groupId>
                <artifactId>c12-core-auth</artifactId>
                <version>${c12-core.version}</version>
            </dependency>
            <dependency>
                <groupId>com.chinaservices.wms</groupId>
                <artifactId>c12-core-sse</artifactId>
                <version>${c12-core.version}</version>
            </dependency>
            <dependency>
                <groupId>com.chinaservices.wms</groupId>
                <artifactId>c12-web-starter</artifactId>
                <version>${c12-core.version}</version>
            </dependency>
            <dependency>
                <groupId>com.chinaservices.wms</groupId>
                <artifactId>c12-core-job</artifactId>
                <version>${c12-core.version}</version>
            </dependency>
            <dependency>
                <groupId>p6spy</groupId>
                <artifactId>p6spy</artifactId>
                <version>${p6spy.version}</version>
            </dependency>
            <dependency>
                <groupId>org.junit.jupiter</groupId>
                <artifactId>junit-jupiter</artifactId>
                <version>${junit-jupiter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.zxing</groupId>
                <artifactId>core</artifactId>
                <version>${core.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.zxing</groupId>
                <artifactId>javase</artifactId>
                <version>${javase.version}</version>
            </dependency>
            <dependency>
                <groupId>com.chinaservices</groupId>
                <artifactId>c12-export-core</artifactId>
                <version>${c12-export-core.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.ulisesbocchio</groupId>
                <artifactId>jasypt-spring-boot-starter</artifactId>
                <version>${jasypt.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>${xxl-job.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <distributionManagement>
        <repository>
            <!-- uat,pre-local,prod -->
            <id>releases</id>
            <!-- pre -->
<!--            <id>maven-releases</id>-->
            <!-- prod -->
<!--            <url>http://139.9.123.100:8081/nexus/content/repositories/releases</url>-->
            <!-- uat -->
            <url>http://192.168.0.37:9999/repository/maven-releases</url>
            <!-- pre-local -->
<!--            <url>http://10.12.7.166:9999/repository/maven-releases</url>-->
            <!-- pre -->
<!--            <url>http://10.12.4.13:8081/repository/maven-releases</url>-->
        </repository>
        <snapshotRepository>
            <!-- uat,pre-local,prod -->
            <id>snapshots</id>
            <!-- pre -->
<!--            <id>maven-snapshots</id>-->
            <!-- prod -->
<!--            <url>http://139.9.123.100:8081/nexus/content/repositories/snapshots</url>-->
            <!-- uat -->
            <url>http://192.168.0.37:9999/repository/maven-snapshots</url>
            <!-- pre-local -->
<!--            <url>http://10.12.7.166:9999/repository/maven-snapshots</url>-->
            <!-- pre -->
<!--            <url>http://10.12.4.13:8081/repository/maven-snapshots</url>-->
        </snapshotRepository>
    </distributionManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <forceJavacCompilerUse>true</forceJavacCompilerUse>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>

<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.chinaservices.wms</groupId>
        <artifactId>c12-core</artifactId>
        <version>1.0.2-SNAPSHOT</version>
    </parent>

    <artifactId>c12-web-starter</artifactId>

    <properties>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.chinaservices.wms</groupId>
            <artifactId>c12-core-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.chinaservices.wms</groupId>
            <artifactId>c12-core-utils</artifactId>
        </dependency>

        <dependency>
            <groupId>com.chinaservices.wms</groupId>
            <artifactId>c12-core-base</artifactId>
        </dependency>

        <dependency>
            <groupId>com.chinaservices.wms</groupId>
            <artifactId>c12-core-auth</artifactId>
        </dependency>

        <dependency>
            <groupId>com.chinaservices.wms</groupId>
            <artifactId>c12-core-app</artifactId>
        </dependency>

        <dependency>
            <groupId>com.chinaservices.wms</groupId>
            <artifactId>c12-core-jackson</artifactId>
        </dependency>

        <dependency>
            <groupId>com.chinaservices.wms</groupId>
            <artifactId>c12-core-log</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chinaservices.wms</groupId>
            <artifactId>c12-core-balancer</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chinaservices.wms</groupId>
            <artifactId>c12-core-jasypt</artifactId>
        </dependency>
    </dependencies>
</project>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SSE POST客户端</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        #status { padding: 10px; margin: 10px 0; border-radius: 4px; font-weight: bold; }
        .connected { background: #d4edda; color: #155724; }
        .disconnected { background: #f8d7da; color: #721c24; }
        .connecting { background: #fff3cd; color: #856404; }
        #messages { height: 400px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; margin-top: 10px; font-family: monospace; font-size: 12px; }
        .message { margin-bottom: 3px; }
        .error { color: red; }
        .success { color: green; }
        .info { color: blue; }
        .warning { color: orange; }
        button { padding: 8px 16px; margin: 5px; }
    </style>
</head>
<body>
    <h1>SSE POST客户端测试</h1>
    
    <div id="status" class="disconnected">状态: 未连接</div>
    
    <div>
        <button id="connectBtn">连接</button>
        <button id="disconnectBtn" disabled>断开</button>
        <button id="clearBtn">清空日志</button>
    </div>
    
    <h3>连接日志:</h3>
    <div id="messages"></div>
    
    <script>
        // 配置
        const config = {
            sseUrl: 'http://127.0.0.1:8082/auth/api/sse/connect',
            headers: {
                'type': 'PC',
                'X-Session-ID': 'web_session_' + Date.now(),
                'X-Client-ID': 'web_client_' + Date.now(),
                'clientType': 'web',
                '_sid': '3609071b-2a40-41d2-9aca-a0e537871700'
            }
        };
        
        // 全局变量
        let abortController = null;
        let connectionId = null;
        
        // DOM元素
        const statusEl = document.getElementById('status');
        const messagesEl = document.getElementById('messages');
        const connectBtn = document.getElementById('connectBtn');
        const disconnectBtn = document.getElementById('disconnectBtn');
        const clearBtn = document.getElementById('clearBtn');
        
        function log(message, type = 'info') {
            const now = new Date().toLocaleTimeString();
            const messageEl = document.createElement('div');
            messageEl.className = `message ${type}`;
            messageEl.innerHTML = `[${now}] ${message}`;
            messagesEl.appendChild(messageEl);
            messagesEl.scrollTop = messagesEl.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
        
        function updateStatus(status, text) {
            statusEl.className = status;
            statusEl.textContent = `状态: ${text || status}`;
        }
        
        function connect() {
            if (abortController) {
                abortController.abort();
            }
            
            updateStatus('connecting', '连接中...');
            log('开始连接: ' + config.sseUrl, 'info');
            log('使用Headers: ' + JSON.stringify(config.headers), 'info');
            
            abortController = new AbortController();
            
            fetch(config.sseUrl, {
                method: 'POST',
                headers: {
                    ...config.headers,
                    'Accept': 'text/event-stream',
                    'Cache-Control': 'no-cache'
                },
                credentials: 'include',
                signal: abortController.signal
            }).then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                log('连接建立成功，开始读取数据流', 'success');
                updateStatus('connected', '已连接');
                connectBtn.disabled = true;
                disconnectBtn.disabled = false;
                
                return response.body;
            }).then(body => {
                const reader = body.getReader();
                const decoder = new TextDecoder();
                
                function readStream() {
                    return reader.read().then(({ done, value }) => {
                        if (done) {
                            log('数据流结束', 'info');
                            handleDisconnection();
                            return;
                        }
                        
                        const chunk = decoder.decode(value, { stream: true });
                        processSSEChunk(chunk);
                        
                        return readStream();
                    });
                }
                
                return readStream();
            }).catch(error => {
                if (error.name === 'AbortError') {
                    log('连接被主动取消', 'info');
                } else {
                    log('连接失败: ' + error.message, 'error');
                }
                handleDisconnection();
            });
        }
        
        // 处理SSE数据块
        let sseBuffer = '';
        function processSSEChunk(chunk) {
            sseBuffer += chunk;
            const lines = sseBuffer.split('\n');
            sseBuffer = lines.pop(); // 保留最后一个可能不完整的行
            
            let eventType = '';
            let eventData = '';
            
            for (const line of lines) {
                if (line.startsWith('event:')) {
                    eventType = line.substring(6).trim();
                } else if (line.startsWith('data:')) {
                    eventData = line.substring(5).trim();
                } else if (line === '') {
                    // 空行表示事件结束
                    if (eventData) {
                        handleSSEEvent(eventType, eventData);
                        eventType = '';
                        eventData = '';
                    }
                }
            }
        }
        
        // 处理SSE事件
        function handleSSEEvent(eventType, eventData) {
            if (eventType === 'connection') {
                try {
                    const data = JSON.parse(eventData);
                    connectionId = data.connectionId;
                    log('连接确认: ' + eventData, 'success');
                } catch (err) {
                    log('连接确认: ' + eventData, 'success');
                }
            } else {
                try {
                    const data = JSON.parse(eventData);
                    const messageType = data.type || 'DEFAULT';
                    
                    if (messageType === 'HEARTBEAT') {
                        log('心跳: ' + (data.message || 'heartbeat'), 'info');
                    } else {
                        log('服务器消息: ' + eventData, 'success');
                    }
                } catch (err) {
                    log('服务器消息: ' + eventData, 'success');
                }
            }
        }
        
        function disconnect() {
            log('主动断开连接', 'info');
            if (abortController) {
                abortController.abort();
                abortController = null;
            }
            handleDisconnection();
        }
        
        function handleDisconnection() {
            updateStatus('disconnected', '已断开');
            connectBtn.disabled = false;
            disconnectBtn.disabled = true;
        }
        
        function clearMessages() {
            messagesEl.innerHTML = '';
        }
        
        // 事件监听
        connectBtn.addEventListener('click', connect);
        disconnectBtn.addEventListener('click', disconnect);
        clearBtn.addEventListener('click', clearMessages);
        
        // 页面加载完成
        window.addEventListener('load', function() {
            log('SSE POST客户端加载完成', 'info');
            log('使用POST请求和Headers传递参数', 'info');
        });
        
        // 页面关闭前清理
        window.addEventListener('beforeunload', function() {
            if (abortController) {
                disconnect();
            }
        });
    </script>
</body>
</html>

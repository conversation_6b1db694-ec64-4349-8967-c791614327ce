package com.chinaservices.balancer;

import com.chinaservices.balancer.IpPrefixLoadBalancer;
import com.chinaservices.balancer.config.IpPrefixLoadBalancerProperties;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.cloud.client.DefaultServiceInstance;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.cloud.client.loadbalancer.Request;
import org.springframework.cloud.client.loadbalancer.Response;
import org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * IP前缀负载均衡器测试类
 * 
 * <AUTHOR>
 */
class IpPrefixLoadBalancerTest {

    @Mock
    private ObjectProvider<ServiceInstanceListSupplier> serviceInstanceListSupplierProvider;

    @Mock
    private ServiceInstanceListSupplier serviceInstanceListSupplier;

    @Mock
    private Request request;

    private IpPrefixLoadBalancerProperties properties;
    private IpPrefixLoadBalancer loadBalancer;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);

        properties = new IpPrefixLoadBalancerProperties();
        properties.setIpPrefixes(Arrays.asList("192.168.0.", "10.0.0."));

        when(serviceInstanceListSupplierProvider.getIfAvailable()).thenReturn(serviceInstanceListSupplier);

        loadBalancer = new IpPrefixLoadBalancer(serviceInstanceListSupplierProvider, properties);
    }

    @Test
    void testChooseWithPreferredInstances() {
        // 准备测试数据
        List<ServiceInstance> instances = Arrays.asList(
            new DefaultServiceInstance("instance1", "test-service", "*************", 8080, false),
            new DefaultServiceInstance("instance2", "test-service", "************", 8080, false),
            new DefaultServiceInstance("instance3", "test-service", "**********", 8080, false)
        );

        when(serviceInstanceListSupplier.get(request)).thenReturn(Flux.just(instances));

        // 执行测试
        Mono<Response<ServiceInstance>> result = loadBalancer.choose(request);
        Response<ServiceInstance> response = result.block();

        // 验证结果
        assertNotNull(response);
        assertTrue(response.hasServer());
        
        ServiceInstance selectedInstance = response.getServer();
        String host = selectedInstance.getHost();
        
        // 验证选择的实例符合IP前缀要求
        assertTrue(host.startsWith("192.168.0.") || host.startsWith("10.0.0."),
                "Selected instance should match configured IP prefixes, but got: " + host);
    }

    @Test
    void testChooseWithoutPreferredInstances() {
        // 准备测试数据 - 没有匹配前缀的实例
        List<ServiceInstance> instances = Arrays.asList(
            new DefaultServiceInstance("instance1", "test-service", "************", 8080, false),
            new DefaultServiceInstance("instance2", "test-service", "************", 8080, false)
        );

        when(serviceInstanceListSupplier.get(request)).thenReturn(Flux.just(instances));

        // 执行测试
        Mono<Response<ServiceInstance>> result = loadBalancer.choose(request);
        Response<ServiceInstance> response = result.block();

        // 验证结果 - 应该使用降级策略选择实例
        assertNotNull(response);
        assertTrue(response.hasServer());
        
        ServiceInstance selectedInstance = response.getServer();
        assertTrue(selectedInstance.getHost().startsWith("172.1"));
    }

    @Test
    void testChooseWithEmptyPrefixes() {
        // 清空IP前缀配置
        properties.setIpPrefixes(Arrays.asList());

        List<ServiceInstance> instances = Arrays.asList(
            new DefaultServiceInstance("instance1", "test-service", "*************", 8080, false),
            new DefaultServiceInstance("instance2", "test-service", "************", 8080, false)
        );

        when(serviceInstanceListSupplier.get(request)).thenReturn(Flux.just(instances));

        // 执行测试
        Mono<Response<ServiceInstance>> result = loadBalancer.choose(request);
        Response<ServiceInstance> response = result.block();

        // 验证结果 - 应该正常选择实例
        assertNotNull(response);
        assertTrue(response.hasServer());
    }

    @Test
    void testChooseWithEmptyInstances() {
        when(serviceInstanceListSupplier.get(request)).thenReturn(Flux.just(Arrays.asList()));

        // 执行测试
        Mono<Response<ServiceInstance>> result = loadBalancer.choose(request);
        Response<ServiceInstance> response = result.block();

        // 验证结果 - 应该返回空响应
        assertNotNull(response);
        assertFalse(response.hasServer());
    }
}

package com.chinaservices.balancer;

import com.chinaservices.balancer.config.IpPrefixLoadBalancerProperties;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.cloud.client.loadbalancer.DefaultResponse;
import org.springframework.cloud.client.loadbalancer.EmptyResponse;
import org.springframework.cloud.client.loadbalancer.Request;
import org.springframework.cloud.client.loadbalancer.Response;
import org.springframework.cloud.loadbalancer.core.ReactorServiceInstanceLoadBalancer;
import org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier;
import org.springframework.util.PatternMatchUtils;
import reactor.core.publisher.Mono;

import java.net.InetAddress;
import java.net.NetworkInterface;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;
import java.util.Random;
import java.util.stream.Collectors;

/**
 * IP前缀优先负载均衡器
 * 
 * <AUTHOR>
 */
public class IpPrefixLoadBalancer implements ReactorServiceInstanceLoadBalancer {

    public static final String LOCAL_HOST = "127.0.0.1";


    private final ObjectProvider<ServiceInstanceListSupplier> serviceInstanceListSupplierProvider;
    private final IpPrefixLoadBalancerProperties properties;
    private final Random random = new Random();

    public IpPrefixLoadBalancer(ObjectProvider<ServiceInstanceListSupplier> serviceInstanceListSupplierProvider,
                               IpPrefixLoadBalancerProperties properties) {
        this.serviceInstanceListSupplierProvider = serviceInstanceListSupplierProvider;
        this.properties = properties;
    }

    @Override
    public Mono<Response<ServiceInstance>> choose(Request request) {
        ServiceInstanceListSupplier supplier = serviceInstanceListSupplierProvider.getIfAvailable();
        if (supplier == null) {
            return Mono.just(new EmptyResponse());
        }

        return supplier.get(request).next().map(this::selectInstance);
    }

    private Response<ServiceInstance> selectInstance(List<ServiceInstance> instances) {
        if (instances.isEmpty()) {
            return new EmptyResponse();
        }
        List<String> ipList = getLocalIP();
        if(!ipList.isEmpty()){
            // 1. 优先选择本地IP实例
            List<ServiceInstance> localInstances = instances.stream()
                    .filter(instance -> ipList.contains(instance.getHost()))
                    .toList();
            if (!localInstances.isEmpty()) {
                return new DefaultResponse(localInstances.get(random.nextInt(localInstances.size())));
            }
        }
        // 2. 获取配置逻辑
        List<String> priorIpPattern = properties.getIpPrefixes();
        List<ServiceInstance> preferredInstances = instances.stream()
                .filter(instance -> priorIpPattern.stream()
                        .anyMatch(prefix -> instance.getHost().startsWith(prefix)))
                .toList();
        List<ServiceInstance> targetInstances = preferredInstances.isEmpty() ? instances : preferredInstances;
        return new DefaultResponse(targetInstances.get(random.nextInt(targetInstances.size())));
    }

    /**
     * 获取本机所有IP
     * @return
     */
    public static List<String> getLocalIP() {
        List<String> ipList = new ArrayList<>();
        InetAddress ip;
        try {
            Enumeration<NetworkInterface> netInterfaces = NetworkInterface.getNetworkInterfaces();
            while (netInterfaces.hasMoreElements()) {
                NetworkInterface ni = netInterfaces.nextElement();
                // 遍历所有ip
                Enumeration<InetAddress> ips = ni.getInetAddresses();
                while (ips.hasMoreElements()) {
                    ip =  ips.nextElement();
                    if (null == ip) {
                        continue;
                    }
                    String sIP = ip.getHostAddress();
                    if(sIP == null || sIP.contains(":")) {
                        continue;
                    }
                    ipList.add(sIP);
                }
            }
        } catch (Exception e) {
            ipList.add(LOCAL_HOST);
        }
        return ipList;
    }
}

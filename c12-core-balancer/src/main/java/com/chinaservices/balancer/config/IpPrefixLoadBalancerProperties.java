package com.chinaservices.balancer.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;

import java.util.ArrayList;
import java.util.List;

/**
 * IP前缀负载均衡配置
 *
 * <AUTHOR>
 */
@Getter
@Setter
@ConfigurationProperties(prefix = "c12.loadbalancer")
@RefreshScope
public class IpPrefixLoadBalancerProperties {

    /**
     * 是否启用IP前缀负载均衡
     */
    boolean enabled = false;

    /**
     * 优先路由的IP前缀列表
     */
    private List<String> ipPrefixes = new ArrayList<>();
}

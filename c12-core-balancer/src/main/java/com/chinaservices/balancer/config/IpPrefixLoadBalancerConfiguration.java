package com.chinaservices.balancer.config;

import com.chinaservices.balancer.IpPrefixLoadBalancer;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cloud.client.ConditionalOnDiscoveryEnabled;
import org.springframework.cloud.loadbalancer.core.ReactorServiceInstanceLoadBalancer;
import org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier;
import org.springframework.cloud.loadbalancer.support.LoadBalancerClientFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;

/**
 * IP前缀负载均衡器配置类
 * 
 * <AUTHOR>
 */
@Configuration
@ConditionalOnDiscoveryEnabled
@ConditionalOnProperty(prefix = "c12.loadbalancer", name = "enabled", havingValue = "true")
public class IpPrefixLoadBalancerConfiguration {

    @Bean
    public ReactorServiceInstanceLoadBalancer ipPrefixLoadBalancer(
            Environment environment,
            LoadBalancerClientFactory loadBalancerClientFactory,
            IpPrefixLoadBalancerProperties properties) {
        String name = environment.getProperty(LoadBalancerClientFactory.PROPERTY_NAME);
        ObjectProvider<ServiceInstanceListSupplier> provider = loadBalancerClientFactory
                .getLazyProvider(name, ServiceInstanceListSupplier.class);
        return new IpPrefixLoadBalancer(provider, properties);
    }
}

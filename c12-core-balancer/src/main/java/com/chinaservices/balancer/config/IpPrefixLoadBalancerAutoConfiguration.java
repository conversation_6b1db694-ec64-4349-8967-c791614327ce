package com.chinaservices.balancer.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.client.ConditionalOnDiscoveryEnabled;
import org.springframework.cloud.loadbalancer.annotation.LoadBalancerClients;
import org.springframework.cloud.loadbalancer.core.ReactorServiceInstanceLoadBalancer;
import org.springframework.context.annotation.Configuration;

/**
 * IP前缀负载均衡器自动配置
 *
 * <AUTHOR>
 */
@Configuration
@ConditionalOnClass(ReactorServiceInstanceLoadBalancer.class)
@ConditionalOnDiscoveryEnabled
@EnableConfigurationProperties(IpPrefixLoadBalancerProperties.class)
@LoadBalancerClients(defaultConfiguration = IpPrefixLoadBalancerConfiguration.class)
public class IpPrefixLoadBalancerAutoConfiguration {
}

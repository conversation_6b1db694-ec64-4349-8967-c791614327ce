# C12 Core Balancer

基于 IP 前缀的负载均衡器，优先路由到指定 IP 前缀的服务实例。

## 功能

- **IP 前缀优先路由**: 优先选择匹配 IP 前缀的服务实例
- **自动降级**: 没有匹配实例时自动选择其他实例
- **零侵入**: 无需修改现有 Feign 代码

## 使用

### 1. 添加依赖

```xml
<dependency>
    <groupId>com.chinaservices.wms</groupId>
    <artifactId>c12-core-balancer</artifactId>
</dependency>
```

### 2. 在 Nacos 中配置

```yaml
c12:
  loadbalancer:
    ip-prefixes:
      - "192.168.0."    # 本地开发环境
      - "10.0.0."       # 内网环境
```

### 3. 自动生效

所有 Feign 调用会自动优先路由到匹配 IP 前缀的实例。

## 工作原理

1. 获取所有服务实例
2. **优先选择本地 IP 实例**（如果存在）
3. 筛选匹配 IP 前缀的实例
4. 优先从匹配的实例中随机选择
5. 没有匹配实例时从所有实例中选择

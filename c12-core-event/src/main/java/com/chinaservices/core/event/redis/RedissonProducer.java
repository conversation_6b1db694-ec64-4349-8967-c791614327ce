package com.chinaservices.core.event.redis;

import lombok.AllArgsConstructor;
import org.redisson.api.*;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

@AllArgsConstructor
public class RedissonProducer {

    private final RedissonClient redisson;

    public void sendMessageAsync(String topic, String message) {
        redisson.getBlockingQueue(topic).add(message);
    }

    public void subscribe(String topic) {
        RBlockingQueue<String> queue = redisson.getBlockingQueue(topic);
        consumeNext(queue);  // 启动异步消费
    }

    private void consumeNext(RBlockingQueue<String> queue) {
        queue.takeAsync().whenComplete((msg, ex) -> {
            if (ex == null) {
                RedisMessageListener.onMessage(msg);
                consumeNext(queue);
            } else {
                // 出现异常时延迟重试
                CompletableFuture.delayedExecutor(1, TimeUnit.SECONDS).execute(() -> consumeNext(queue));
            }
        });
    }
}

package com.chinaservices.core.event.redis;

import cn.hutool.core.date.StopWatch;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.ClassLoaderUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.chinaservices.core.event.bus.Subscriber;
import com.chinaservices.core.event.bus.SubscriberRegistry;
import com.chinaservices.core.event.model.EventBusDTO;
import com.chinaservices.core.exception.ServiceException;
import com.chinaservices.session.SessionContext;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.listener.MessageListener;

import java.lang.reflect.InvocationTargetException;
import java.util.Iterator;
import java.util.Map;

@Slf4j
public class RedisMessageListener {

    private static SubscriberRegistry subscriberRegistry;

    private static final Map<String, Class<?>> MAP = Maps.newConcurrentMap();

    public static void init() {
        subscriberRegistry = new SubscriberRegistry(SpringUtil.getBean(RedisEventBus.class));
    }

    public static void onMessage(String message) {
        log.info("event-bus消费层，消费数据 ==>{}", message);
        try {
            EventBusDTO eventBusDTO = JSONUtil.toBean(message, EventBusDTO.class);
            Class<?> clazz = MAP.get(eventBusDTO.getClassName());
            if (clazz == null) {
                clazz = ClassLoaderUtil.loadClass(eventBusDTO.getClassName());
            }
            Object obj = JSON.parseObject(JSONUtil.toJsonStr(eventBusDTO.getEvent()), clazz);
            Iterator<Subscriber> eventSubscribers = subscriberRegistry.getSubscribers(obj);
            while (eventSubscribers.hasNext()) {
                Subscriber subscriber = eventSubscribers.next();
                if (subscriber == null) {
                    log.info("未获取到事件消费端，不处理 === 》事件类名：{}", eventBusDTO.getClassName());
                    continue;
                }
                StopWatch stopWatch = new StopWatch();
                stopWatch.start();
                log.info("执行事件消费，事件类名：{}，消费数据=》{}，target={}", eventBusDTO.getClassName(), JSONUtil.toJsonStr(obj), subscriber.getTarget());
                SessionContext.put(eventBusDTO.getSessionUserInfo());
                subscriber.getMethod().invoke(subscriber.getTarget(), obj);
                SessionContext.remove();
                stopWatch.stop();
                log.info("event-bus消费层消费完成：{} 秒,数据：{}", stopWatch.getTotalTimeSeconds(), message);
            }
        } catch (InvocationTargetException e) {
            if (ExceptionUtil.getRootCause(e) instanceof ServiceException) {
                log.warn("事件中心消费完成：{}", ExceptionUtil.getRootCause(e).getMessage());
                return;
            }
            log.error("事件中心消异常:", e);
        } catch (Exception e) {
            log.error("事件中心消异常：", e);
        }
    }
}

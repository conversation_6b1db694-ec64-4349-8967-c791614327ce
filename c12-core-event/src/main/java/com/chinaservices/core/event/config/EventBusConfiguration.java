package com.chinaservices.core.event.config;

import cn.hutool.extra.spring.SpringUtil;
import com.chinaservices.core.event.constant.RedisEventBusConstant;
import com.chinaservices.core.event.redis.RedisEventBus;
import com.chinaservices.core.event.redis.RedisMessageListener;
import com.chinaservices.core.event.redis.RedissonProducer;
import com.google.common.eventbus.EventBus;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.boot.web.context.WebServerInitializedEvent;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.EventListener;
import org.springframework.core.annotation.Order;
import org.springframework.scheduling.annotation.Async;
import java.util.Arrays;
import java.util.List;

@Configuration
@Slf4j
public class EventBusConfiguration {

    @Bean
    public EventBus eventBus(RedissonProducer redissonProducer) {
        return new RedisEventBus(redissonProducer);
    }

    @Bean
    public RedissonProducer redissonProducer(RedissonClient redissonClient) {
        return new RedissonProducer(redissonClient);
    }

    @Async
    @Order
    @EventListener(WebServerInitializedEvent.class)
    public void initEventBusMethodRepository() {
        log.info("加载event-bus事件类========》》》》》》》");
        ApplicationContext context = SpringUtil.getApplicationContext();
        if(null == context){
            return;
        }
        List<String> beanNames = Arrays.stream(context.getBeanNamesForType(Object.class, false, true))
                .filter(s -> !s.toLowerCase().contains("cache")).toList();
        beanNames.forEach(bean -> SpringUtil.getBean(EventBus.class).register(SpringUtil.getBean(bean)));
        RedisMessageListener.init();
        SpringUtil.getBean(RedissonProducer.class).subscribe(RedisEventBusConstant.REDIS_EVENT_BUS);
    }
}

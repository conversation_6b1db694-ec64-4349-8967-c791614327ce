package com.chinaservices.job.config;

import com.chinaservices.job.aspect.XxlJobAspect;
import com.chinaservices.job.properties.XxlJobProperties;
import com.chinaservices.module.service.VirtualLoginService;
import com.chinaservices.module.service.impl.VirtualLoginServiceImpl;
import com.xxl.job.core.executor.impl.XxlJobSpringExecutor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;

@Configuration
@EnableConfigurationProperties({XxlJobProperties.class})
public class XxlJobCoreConfig {

    @Value("${spring.application.name}")
    private String appName;

    @Bean
    public XxlJobSpringExecutor xxlJobExecutor(XxlJobProperties xxlJobProperties) {
        XxlJobSpringExecutor xxlJobSpringExecutor = new XxlJobSpringExecutor();
        xxlJobSpringExecutor.setAdminAddresses(xxlJobProperties.getAdminAddresses());
        xxlJobSpringExecutor.setAccessToken(xxlJobProperties.getAccessToken());
        xxlJobSpringExecutor.setAppname(appName + "-executor");
        xxlJobSpringExecutor.setLogPath(xxlJobProperties.getLogPath());
        xxlJobSpringExecutor.setLogRetentionDays(xxlJobProperties.getLogRetentionDays());
        return xxlJobSpringExecutor;
    }

    @Bean
    public VirtualLoginService virtualLoginService() {
        return new VirtualLoginServiceImpl();
    }

    @Bean
    @DependsOn("virtualLoginService")
    public XxlJobAspect xxlJobAspect(XxlJobProperties xxlJobProperties, VirtualLoginService virtualLoginService) {
        return new XxlJobAspect(xxlJobProperties, virtualLoginService);
    }
}

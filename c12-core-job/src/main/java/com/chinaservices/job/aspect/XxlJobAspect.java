package com.chinaservices.job.aspect;

import com.chinaservices.job.properties.XxlJobProperties;
import com.chinaservices.module.service.VirtualLoginService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;

@Aspect
@Slf4j
public class XxlJobAspect {

    public final XxlJobProperties properties;

    private final VirtualLoginService virtualLoginService;

    public XxlJobAspect(XxlJobProperties properties, VirtualLoginService virtualLoginService) {
        this.properties = properties;
        this.virtualLoginService = virtualLoginService;
    }

    @Pointcut("@annotation(com.xxl.job.core.handler.annotation.XxlJob)")
    public void xxlJobPointcut() {

    }

    @Around("xxlJobPointcut() && @annotation(anno)")
    public Object traceBackgroundThread(ProceedingJoinPoint pjp, XxlJob anno) throws Throwable {
        String jobName = anno.value();
        log.info("xxl job start {}", jobName);
        try {
            virtualLoginService.virtualLogin("job");
            Object proceed = pjp.proceed();
            log.info("xxl job end {}", jobName);
            return proceed;
        } catch (Throwable ex) {
            log.error(String.format("xxl job error %s", jobName), ex);
            throw ex;
        }
    }
}
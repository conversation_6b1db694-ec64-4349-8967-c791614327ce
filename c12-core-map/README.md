# C12 Core Map - 基于第三方库的仓库路径规划

## 概述

C12 Core Map 是一个专为仓库管理系统设计的路径规划包，使用成熟的第三方库（JGraphT）来确保算法的可靠性和性能。

## 🎯 核心功能

### 双重返回值设计
1. **优化后的库位访问顺序** - TSP算法优化的库位访问序列
2. **完整的路径轨迹点位** - 从起点到终点的每一步移动轨迹

### 为什么使用第三方库
- ✅ **算法可靠性**: 使用经过15年验证的JGraphT库
- ✅ **性能优化**: 工业级算法优化，无需自己维护
- ✅ **降低风险**: 避免自研算法的bug和维护成本
- ✅ **社区支持**: 活跃的开源社区，持续更新

## 快速开始

### 1. 添加依赖

```xml
<dependency>
    <groupId>org.jgrapht</groupId>
    <artifactId>jgrapht-core</artifactId>
    <version>1.5.2</version>
</dependency>
```

### 2. 创建仓库网格

```java
// 创建仓库网格
WarehouseGrid grid = new WarehouseGrid(100, 80);

// 设置起始点
grid.setStartPoint(new GridPoint(50, 75));

// 添加货架（障碍物）
grid.addRectangleShelf("SHELF_A", 10, 10, 8, 25);

// 添加库位面
grid.addShelfFace("SHELF_A", 9, 12, "LOC_A01", "A01");
grid.addShelfFace("SHELF_A", 9, 15, "LOC_A02", "A02");
```

### 3. 创建路径服务

```java
// 创建基于JGraphT的路径服务
JGraphTPathService pathService = new JGraphTPathService(grid);
```

### 4. 计算最佳路径

```java
// 定义需要访问的库位
List<String> locationIds = Arrays.asList("LOC_A01", "LOC_A02", "LOC_B01");
GridPoint startPoint = grid.getStartPoint();

// 计算路径
PathPlanningResult result = pathService.calculateOptimalPath(locationIds, startPoint);

if (result.isSuccess()) {
    System.out.println("总距离: " + result.getTotalDistance());
    System.out.println("计算耗时: " + result.getComputationTime() + "ms");
    
    // 🎯 返回值1: 优化后的库位访问顺序
    List<GridPoint> locationOrder = result.getOptimizedLocationOrder();
    System.out.println("库位访问顺序: " + locationOrder.size() + " 个库位");
    
    // 🛤️ 返回值2: 完整的路径轨迹点位
    List<GridPoint> trajectory = result.getPathTrajectory();
    System.out.println("路径轨迹: " + trajectory.size() + " 个移动步骤");
}
```

## 🎮 运行示例

### 基础演示
```bash
cd c12-core-map
mvn compile
mvn exec:java -Dexec.mainClass="com.chinaservices.wms.map.ThirdPartyExample"
```

### 路径轨迹详细演示
```bash
mvn exec:java -Dexec.mainClass="com.chinaservices.wms.map.PathTrajectoryExample"
```

## 📊 应用场景

### 1. WMS系统集成
```java
// 获取优化后的库位访问顺序，用于拣货指导
List<GridPoint> locationOrder = result.getOptimizedLocationOrder();
for (GridPoint point : locationOrder) {
    // 指导拣货员按顺序访问库位
    System.out.println("下一个库位: " + point);
}
```

### 2. AGV/机器人导航
```java
// 获取详细的移动轨迹，用于机器人导航
List<GridPoint> trajectory = result.getPathTrajectory();
for (int i = 1; i < trajectory.size(); i++) {
    GridPoint from = trajectory.get(i-1);
    GridPoint to = trajectory.get(i);
    // 发送移动指令给AGV
    sendMoveCommand(from, to);
}
```

### 3. 移动端显示
```java
// 在移动设备上显示路径信息
String pathInfo = String.format(
    "总距离: %.1f米, 预计时间: %.1f秒, 库位数量: %d",
    result.getTotalDistance(),
    result.getTotalDistance() / 1.5, // 假设行走速度1.5m/s
    result.getOptimizedLocationOrder().size()
);
```

## 🏗️ 项目结构

```
c12-core-map/
├── src/main/java/com/chinaservices/wms/map/
│   ├── grid/                           # 网格系统
│   │   ├── GridPoint.java              # 三维网格坐标
│   │   └── WarehouseGrid.java          # 仓库网格管理
│   ├── thirdparty/                     # 第三方库集成
│   │   └── JGraphTPathService.java     # JGraphT路径服务
│   ├── ThirdPartyExample.java          # 基础使用示例
│   └── PathTrajectoryExample.java      # 路径轨迹详细演示
├── pom.xml                             # Maven配置
└── README.md                           # 项目文档
```

## 🔧 技术优势

### 基于JGraphT的专业算法
- **Dijkstra算法**: 保证最短路径的正确性
- **TSP优化**: 2-opt算法优化访问顺序
- **图结构**: 高效的图数据结构和算法

### 性能特点
- **时间复杂度**: O(V log V + E) (JGraphT优化后)
- **空间复杂度**: O(V + E)
- **适用规模**: 支持大规模仓库（10万+节点）

## 📈 返回值详解

### PathPlanningResult 包含：

| 字段 | 类型 | 说明 | 用途 |
|------|------|------|------|
| `optimizedLocationOrder` | `List<GridPoint>` | 优化后的库位访问顺序 | WMS拣货指导、任务分配 |
| `pathTrajectory` | `List<GridPoint>` | 完整的路径轨迹点位 | AGV导航、路径可视化 |
| `totalDistance` | `double` | 总距离 | 时间估算、成本计算 |
| `computationTime` | `long` | 计算耗时(ms) | 性能监控 |
| `algorithm` | `String` | 算法类型 | 系统日志、调试 |

## 依赖

- **Java 21+**: 现代Java特性
- **JGraphT 1.5.2**: 图算法库
- **Maven**: 构建工具

## 许可证

本项目采用内部许可证，仅供 C12 项目内部使用。

## 更新日志

### v2.1.0 (2025-01-24)
- ✅ **双重返回值**: 库位访问顺序 + 路径轨迹点位
- 🎯 **精确轨迹**: 每一步移动的详细坐标
- 📱 **应用场景**: WMS集成、AGV导航、移动端显示
- 🔧 **完整示例**: PathTrajectoryExample.java

### v2.0.0 (2025-01-24)
- 🔄 **重大重构**: 移除所有自研算法
- ✅ **第三方库**: 采用成熟的JGraphT库
- 🛡️ **风险降低**: 消除自研算法的业务风险

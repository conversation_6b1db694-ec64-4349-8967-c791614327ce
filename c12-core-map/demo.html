<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>C12 Core Map - 仓库路径规划演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .main-content {
            display: grid;
            grid-template-columns: 1fr 400px;
            gap: 30px;
            padding: 30px;
        }
        
        .warehouse-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
        }
        
        .warehouse-grid {
            display: grid;
            grid-template-columns: repeat(25, 20px);
            gap: 1px;
            background: #ddd;
            padding: 10px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .cell {
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            font-weight: bold;
        }
        
        .walkable { background: #e8f5e8; }
        .obstacle { background: #333; color: white; }
        .shelf-face { background: #ffd700; color: #333; }
        .start { background: #28a745; color: white; }
        .path { background: #007bff; color: white; }
        .location { background: #dc3545; color: white; }
        
        .control-panel {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
        }
        
        .section-title {
            font-size: 1.5em;
            margin-bottom: 15px;
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 5px;
        }
        
        .location-list {
            margin: 15px 0;
        }
        
        .location-item {
            display: flex;
            align-items: center;
            padding: 8px;
            margin: 5px 0;
            background: white;
            border-radius: 5px;
            border-left: 4px solid #3498db;
        }
        
        .location-checkbox {
            margin-right: 10px;
        }
        
        .btn {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: all 0.3s;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
        }
        
        .result-panel {
            margin-top: 20px;
            padding: 15px;
            background: white;
            border-radius: 8px;
            border-left: 4px solid #27ae60;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin: 20px 0;
        }
        
        .stat-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .stat-value {
            font-size: 2em;
            font-weight: bold;
            color: #3498db;
        }
        
        .stat-label {
            color: #7f8c8d;
            margin-top: 5px;
        }
        
        .legend {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin: 15px 0;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 3px;
        }
        
        .trajectory-list {
            max-height: 200px;
            overflow-y: auto;
            background: #f8f9fa;
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
        }
        
        .trajectory-step {
            padding: 3px 0;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🗺️ C12 Core Map</h1>
            <p>基于JGraphT的智能仓库路径规划系统</p>
        </div>
        
        <div class="main-content">
            <div class="warehouse-section">
                <h2 class="section-title">🏭 仓库布局</h2>
                
                <div class="legend">
                    <div class="legend-item">
                        <div class="legend-color start"></div>
                        <span>起始点</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color walkable"></div>
                        <span>通道</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color obstacle"></div>
                        <span>货架</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color shelf-face"></div>
                        <span>库位</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color path"></div>
                        <span>路径</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color location"></div>
                        <span>目标库位</span>
                    </div>
                </div>
                
                <div id="warehouse-grid" class="warehouse-grid"></div>
                
                <div class="stats">
                    <div class="stat-card">
                        <div class="stat-value" id="total-distance">0</div>
                        <div class="stat-label">总距离</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="trajectory-steps">0</div>
                        <div class="stat-label">轨迹步数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="computation-time">0ms</div>
                        <div class="stat-label">计算耗时</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="location-count">0</div>
                        <div class="stat-label">库位数量</div>
                    </div>
                </div>
            </div>
            
            <div class="control-panel">
                <h2 class="section-title">🎯 路径规划控制</h2>
                
                <div class="location-list">
                    <h3>选择目标库位：</h3>
                    <div class="location-item">
                        <input type="checkbox" class="location-checkbox" value="LOC_A01" checked>
                        <span>A01 - 货架A库位1</span>
                    </div>
                    <div class="location-item">
                        <input type="checkbox" class="location-checkbox" value="LOC_A02">
                        <span>A02 - 货架A库位2</span>
                    </div>
                    <div class="location-item">
                        <input type="checkbox" class="location-checkbox" value="LOC_A03">
                        <span>A03 - 货架A库位3</span>
                    </div>
                    <div class="location-item">
                        <input type="checkbox" class="location-checkbox" value="LOC_B01">
                        <span>B01 - 货架B库位1</span>
                    </div>
                    <div class="location-item">
                        <input type="checkbox" class="location-checkbox" value="LOC_B02" checked>
                        <span>B02 - 货架B库位2</span>
                    </div>
                    <div class="location-item">
                        <input type="checkbox" class="location-checkbox" value="LOC_C01" checked>
                        <span>C01 - 货架C库位1</span>
                    </div>
                    <div class="location-item">
                        <input type="checkbox" class="location-checkbox" value="LOC_C02">
                        <span>C02 - 货架C库位2</span>
                    </div>
                </div>
                
                <button class="btn btn-success" onclick="calculatePath()">🚀 计算最优路径</button>
                <button class="btn btn-danger" onclick="clearPath()">🧹 清除路径</button>
                <button class="btn" onclick="showDemo()">🎮 演示模式</button>
                
                <div id="result-panel" class="result-panel" style="display: none;">
                    <h3>📊 路径规划结果</h3>
                    <div id="location-order"></div>
                    <div id="trajectory-info"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 仓库配置
        const WAREHOUSE_CONFIG = {
            width: 25,
            height: 20,
            startPoint: {x: 1, y: 10},
            shelves: [
                {name: 'SHELF_A', x: 2, y: 2, width: 8, height: 4},
                {name: 'SHELF_B', x: 15, y: 2, width: 8, height: 6},
                {name: 'SHELF_C', x: 10, y: 13, width: 8, height: 4}
            ],
            locations: [
                {id: 'LOC_A01', code: 'A01', x: 2, y: 3},
                {id: 'LOC_A02', code: 'A02', x: 5, y: 3},
                {id: 'LOC_A03', code: 'A03', x: 8, y: 3},
                {id: 'LOC_B01', code: 'B01', x: 15, y: 4},
                {id: 'LOC_B02', code: 'B02', x: 18, y: 4},
                {id: 'LOC_C01', code: 'C01', x: 12, y: 12},
                {id: 'LOC_C02', code: 'C02', x: 16, y: 12}
            ]
        };

        let currentPath = [];
        let currentLocations = [];

        // 初始化仓库网格
        function initializeWarehouse() {
            const grid = document.getElementById('warehouse-grid');
            grid.innerHTML = '';
            
            for (let y = 0; y < WAREHOUSE_CONFIG.height; y++) {
                for (let x = 0; x < WAREHOUSE_CONFIG.width; x++) {
                    const cell = document.createElement('div');
                    cell.className = 'cell';
                    cell.id = `cell-${x}-${y}`;
                    
                    // 确定单元格类型
                    if (x === WAREHOUSE_CONFIG.startPoint.x && y === WAREHOUSE_CONFIG.startPoint.y) {
                        cell.className += ' start';
                        cell.textContent = 'S';
                    } else if (isObstacle(x, y)) {
                        cell.className += ' obstacle';
                        cell.textContent = '#';
                    } else if (isShelfFace(x, y)) {
                        cell.className += ' shelf-face';
                        const location = getLocationAt(x, y);
                        cell.textContent = location ? location.code : 'L';
                    } else {
                        cell.className += ' walkable';
                        cell.textContent = '.';
                    }
                    
                    grid.appendChild(cell);
                }
            }
        }

        // 检查是否为障碍物
        function isObstacle(x, y) {
            return WAREHOUSE_CONFIG.shelves.some(shelf => 
                x >= shelf.x && x < shelf.x + shelf.width &&
                y >= shelf.y && y < shelf.y + shelf.height
            );
        }

        // 检查是否为库位面
        function isShelfFace(x, y) {
            return WAREHOUSE_CONFIG.locations.some(loc => loc.x === x && loc.y === y);
        }

        // 获取指定位置的库位
        function getLocationAt(x, y) {
            return WAREHOUSE_CONFIG.locations.find(loc => loc.x === x && loc.y === y);
        }

        // 简单的A*路径查找算法
        function findPath(start, end) {
            const openSet = [{...start, g: 0, h: heuristic(start, end), f: heuristic(start, end), parent: null}];
            const closedSet = [];
            
            while (openSet.length > 0) {
                openSet.sort((a, b) => a.f - b.f);
                const current = openSet.shift();
                
                if (current.x === end.x && current.y === end.y) {
                    const path = [];
                    let node = current;
                    while (node) {
                        path.unshift({x: node.x, y: node.y});
                        node = node.parent;
                    }
                    return path;
                }
                
                closedSet.push(current);
                
                const neighbors = getNeighbors(current);
                for (const neighbor of neighbors) {
                    if (closedSet.some(node => node.x === neighbor.x && node.y === neighbor.y)) {
                        continue;
                    }
                    
                    const g = current.g + 1;
                    const h = heuristic(neighbor, end);
                    const f = g + h;
                    
                    const existingNode = openSet.find(node => node.x === neighbor.x && node.y === neighbor.y);
                    if (!existingNode) {
                        openSet.push({...neighbor, g, h, f, parent: current});
                    } else if (g < existingNode.g) {
                        existingNode.g = g;
                        existingNode.f = f;
                        existingNode.parent = current;
                    }
                }
            }
            
            return [];
        }

        // 启发式函数（曼哈顿距离）
        function heuristic(a, b) {
            return Math.abs(a.x - b.x) + Math.abs(a.y - b.y);
        }

        // 获取邻居节点
        function getNeighbors(node) {
            const neighbors = [];
            const directions = [{x: 0, y: 1}, {x: 1, y: 0}, {x: 0, y: -1}, {x: -1, y: 0}];
            
            for (const dir of directions) {
                const x = node.x + dir.x;
                const y = node.y + dir.y;
                
                if (x >= 0 && x < WAREHOUSE_CONFIG.width && 
                    y >= 0 && y < WAREHOUSE_CONFIG.height && 
                    !isObstacle(x, y)) {
                    neighbors.push({x, y});
                }
            }
            
            return neighbors;
        }

        // 计算路径
        function calculatePath() {
            const startTime = Date.now();
            const selectedLocations = Array.from(document.querySelectorAll('.location-checkbox:checked'))
                .map(cb => cb.value);
            
            if (selectedLocations.length === 0) {
                alert('请至少选择一个库位！');
                return;
            }

            // 获取库位坐标
            const locationPoints = selectedLocations.map(id => 
                WAREHOUSE_CONFIG.locations.find(loc => loc.id === id)
            ).filter(loc => loc);

            // 简单的TSP优化（最近邻算法）
            const optimizedOrder = optimizeLocationOrder(locationPoints, WAREHOUSE_CONFIG.startPoint);
            
            // 构建完整路径
            const fullPath = [];
            let currentPoint = WAREHOUSE_CONFIG.startPoint;
            
            for (const location of optimizedOrder) {
                const pathSegment = findPath(currentPoint, location);
                if (pathSegment.length > 0) {
                    if (fullPath.length > 0) {
                        pathSegment.shift(); // 移除重复的起点
                    }
                    fullPath.push(...pathSegment);
                    currentPoint = location;
                }
            }

            const endTime = Date.now();
            const computationTime = endTime - startTime;

            // 更新显示
            currentPath = fullPath;
            currentLocations = optimizedOrder;
            
            displayPath(fullPath, optimizedOrder);
            updateStats(fullPath.length - 1, fullPath.length, computationTime, optimizedOrder.length);
            showResults(optimizedOrder, fullPath);
        }

        // 优化库位访问顺序（最近邻算法）
        function optimizeLocationOrder(locations, start) {
            if (locations.length <= 1) return locations;
            
            const unvisited = [...locations];
            const visited = [];
            let current = start;
            
            while (unvisited.length > 0) {
                let nearest = unvisited[0];
                let minDistance = heuristic(current, nearest);
                
                for (const location of unvisited) {
                    const distance = heuristic(current, location);
                    if (distance < minDistance) {
                        minDistance = distance;
                        nearest = location;
                    }
                }
                
                visited.push(nearest);
                unvisited.splice(unvisited.indexOf(nearest), 1);
                current = nearest;
            }
            
            return visited;
        }

        // 显示路径
        function displayPath(path, locations) {
            // 清除之前的路径
            clearPath();
            
            // 显示路径
            path.forEach((point, index) => {
                const cell = document.getElementById(`cell-${point.x}-${point.y}`);
                if (cell && !cell.classList.contains('start')) {
                    if (locations.some(loc => loc.x === point.x && loc.y === point.y)) {
                        cell.className = 'cell location';
                        const location = getLocationAt(point.x, point.y);
                        cell.textContent = location ? location.code : 'L';
                    } else {
                        cell.className = 'cell path';
                        cell.textContent = '→';
                    }
                }
            });
        }

        // 清除路径
        function clearPath() {
            currentPath = [];
            currentLocations = [];
            
            for (let y = 0; y < WAREHOUSE_CONFIG.height; y++) {
                for (let x = 0; x < WAREHOUSE_CONFIG.width; x++) {
                    const cell = document.getElementById(`cell-${x}-${y}`);
                    if (cell && (cell.classList.contains('path') || cell.classList.contains('location'))) {
                        if (isShelfFace(x, y)) {
                            cell.className = 'cell shelf-face';
                            const location = getLocationAt(x, y);
                            cell.textContent = location ? location.code : 'L';
                        } else {
                            cell.className = 'cell walkable';
                            cell.textContent = '.';
                        }
                    }
                }
            }
            
            updateStats(0, 0, 0, 0);
            document.getElementById('result-panel').style.display = 'none';
        }

        // 更新统计信息
        function updateStats(distance, steps, time, locationCount) {
            document.getElementById('total-distance').textContent = distance;
            document.getElementById('trajectory-steps').textContent = steps;
            document.getElementById('computation-time').textContent = time + 'ms';
            document.getElementById('location-count').textContent = locationCount;
        }

        // 显示结果
        function showResults(optimizedOrder, trajectory) {
            const resultPanel = document.getElementById('result-panel');
            const locationOrderDiv = document.getElementById('location-order');
            const trajectoryInfoDiv = document.getElementById('trajectory-info');
            
            // 显示优化后的库位访问顺序
            locationOrderDiv.innerHTML = '<h4>📍 优化后的库位访问顺序：</h4>';
            optimizedOrder.forEach((location, index) => {
                locationOrderDiv.innerHTML += `<div>${index + 1}. ${location.code} at (${location.x},${location.y})</div>`;
            });
            
            // 显示路径轨迹信息
            trajectoryInfoDiv.innerHTML = '<h4>🛤️ 路径轨迹信息：</h4>';
            trajectoryInfoDiv.innerHTML += `<div>轨迹总长度: ${trajectory.length} 步</div>`;
            
            if (trajectory.length <= 10) {
                trajectoryInfoDiv.innerHTML += '<div class="trajectory-list">';
                trajectory.forEach((point, index) => {
                    trajectoryInfoDiv.innerHTML += `<div class="trajectory-step">步骤 ${index + 1}: (${point.x},${point.y})</div>`;
                });
                trajectoryInfoDiv.innerHTML += '</div>';
            } else {
                trajectoryInfoDiv.innerHTML += '<div>路径过长，仅显示关键点</div>';
            }
            
            resultPanel.style.display = 'block';
        }

        // 演示模式
        function showDemo() {
            // 选择演示库位
            document.querySelectorAll('.location-checkbox').forEach(cb => cb.checked = false);
            document.querySelector('input[value="LOC_A01"]').checked = true;
            document.querySelector('input[value="LOC_B02"]').checked = true;
            document.querySelector('input[value="LOC_C01"]').checked = true;
            
            // 计算路径
            setTimeout(() => calculatePath(), 500);
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeWarehouse();
            
            // 添加库位选择变化监听
            document.querySelectorAll('.location-checkbox').forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    if (currentPath.length > 0) {
                        calculatePath(); // 自动重新计算
                    }
                });
            });
        });
    </script>
</body>
</html>
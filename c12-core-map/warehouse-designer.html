<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>C12 仓库平面图设计器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1600px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2d3436 0%, #636e72 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .main-content {
            display: grid;
            grid-template-columns: 350px 1fr;
            gap: 20px;
            padding: 20px;
        }
        
        .control-panel {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            height: fit-content;
        }
        
        .warehouse-canvas {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            position: relative;
        }
        
        .section-title {
            font-size: 1.3em;
            margin-bottom: 15px;
            color: #2d3436;
            border-bottom: 2px solid #74b9ff;
            padding-bottom: 5px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #2d3436;
        }
        
        .form-group input, .form-group select {
            width: 100%;
            padding: 8px 12px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        
        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #74b9ff;
        }
        
        .btn {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: all 0.3s;
            width: calc(50% - 10px);
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(116, 185, 255, 0.4);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #e17055 0%, #d63031 100%);
        }
        
        .btn-full {
            width: 100%;
        }
        
        .warehouse-grid {
            display: grid;
            gap: 1px;
            background: #ddd;
            padding: 10px;
            border-radius: 8px;
            margin: 10px 0;
            overflow: auto;
            max-height: 600px;
        }
        
        .cell {
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 8px;
            font-weight: bold;
            cursor: pointer;
        }
        
        .walkable { background: #e8f5e8; }
        .obstacle { background: #2d3436; color: white; }
        .shelf-face { background: #fdcb6e; color: #2d3436; }
        .start { background: #00b894; color: white; }
        .path { background: #74b9ff; color: white; }
        .target { background: #e17055; color: white; }
        
        .legend {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin: 15px 0;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 12px;
        }
        
        .legend-color {
            width: 15px;
            height: 15px;
            border-radius: 3px;
        }
        
        .stats-panel {
            background: white;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
            border-left: 4px solid #74b9ff;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-top: 10px;
        }
        
        .stat-item {
            text-align: center;
            padding: 8px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        
        .stat-value {
            font-size: 1.5em;
            font-weight: bold;
            color: #74b9ff;
        }
        
        .stat-label {
            font-size: 12px;
            color: #636e72;
        }
        
        .shelf-list {
            max-height: 200px;
            overflow-y: auto;
            background: white;
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
        }
        
        .shelf-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 5px;
            margin: 3px 0;
            background: #f8f9fa;
            border-radius: 3px;
            font-size: 12px;
        }
        
        .shelf-item button {
            background: #e17055;
            color: white;
            border: none;
            padding: 2px 6px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 10px;
        }
        
        .location-list {
            max-height: 150px;
            overflow-y: auto;
            background: white;
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
        }
        
        .location-item {
            display: flex;
            align-items: center;
            padding: 3px;
            margin: 2px 0;
            font-size: 11px;
        }
        
        .location-checkbox {
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏗️ C12 仓库平面图设计器</h1>
            <p>智能仓库布局设计与路径规划系统</p>
        </div>
        
        <div class="main-content">
            <div class="control-panel">
                <h2 class="section-title">🎛️ 仓库参数设置</h2>
                
                <div class="form-group">
                    <label>仓库宽度 (格)</label>
                    <input type="number" id="warehouse-width" value="30" min="10" max="50">
                </div>
                
                <div class="form-group">
                    <label>仓库高度 (格)</label>
                    <input type="number" id="warehouse-height" value="20" min="10" max="40">
                </div>
                
                <div class="form-group">
                    <label>单元格大小 (px)</label>
                    <input type="number" id="cell-size" value="15" min="10" max="25">
                </div>
                
                <button class="btn btn-full" onclick="initializeWarehouse()">🏗️ 初始化仓库</button>
                
                <h3 class="section-title">📦 货架设置</h3>
                
                <div style="background: #e3f2fd; padding: 10px; border-radius: 5px; margin-bottom: 15px; font-size: 12px;">
                    <strong>💡 提示：</strong>库位将自动在货架的长边生成，符合实际拣货习惯
                </div>
                
                <div class="form-group">
                    <label>货架宽度</label>
                    <input type="number" id="shelf-width" value="6" min="2" max="10">
                </div>
                
                <div class="form-group">
                    <label>货架高度</label>
                    <input type="number" id="shelf-height" value="3" min="2" max="8">
                </div>
                
                <div class="form-group">
                    <label>货架间距</label>
                    <input type="number" id="shelf-spacing" value="3" min="2" max="6">
                </div>
                
                <button class="btn btn-success" onclick="addShelf()">➕ 添加货架</button>
                <button class="btn btn-danger" onclick="clearShelves()">🗑️ 清空货架</button>
                
                <div class="shelf-list" id="shelf-list"></div>
                
                <h3 class="section-title">🎯 起点设置</h3>
                
                <div class="form-group">
                    <label>起点 X 坐标</label>
                    <input type="number" id="start-x" value="1" min="0">
                </div>
                
                <div class="form-group">
                    <label>起点 Y 坐标</label>
                    <input type="number" id="start-y" value="10" min="0">
                </div>
                
                <button class="btn btn-full" onclick="setStartPoint()">📍 设置起点</button>
                
                <h3 class="section-title">📍 库位选择</h3>
                
                <div class="location-list" id="location-list"></div>
                
                <button class="btn btn-success btn-full" onclick="calculatePath()">🚀 计算最优路径</button>
                <button class="btn btn-danger btn-full" onclick="clearPath()">🧹 清除路径</button>
                
                <div class="stats-panel">
                    <h4>📊 统计信息</h4>
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-value" id="total-shelves">0</div>
                            <div class="stat-label">货架数量</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="total-locations">0</div>
                            <div class="stat-label">库位数量</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="path-distance">0</div>
                            <div class="stat-label">路径距离</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="computation-time">0ms</div>
                            <div class="stat-label">计算耗时</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="warehouse-canvas">
                <h2 class="section-title">🏭 仓库平面图</h2>
                
                <div class="legend">
                    <div class="legend-item">
                        <div class="legend-color start"></div>
                        <span>起始点</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color walkable"></div>
                        <span>通道</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color obstacle"></div>
                        <span>货架</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color shelf-face"></div>
                        <span>库位</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color path"></div>
                        <span>路径</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color target"></div>
                        <span>目标库位</span>
                    </div>
                </div>
                
                <div id="warehouse-grid" class="warehouse-grid"></div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let warehouseConfig = {
            width: 30,
            height: 20,
            cellSize: 15,
            startPoint: {x: 1, y: 10},
            shelves: [],
            locations: []
        };
        
        let currentPath = [];
        let shelfCounter = 0;
        
        // 初始化仓库
        function initializeWarehouse() {
            warehouseConfig.width = parseInt(document.getElementById('warehouse-width').value);
            warehouseConfig.height = parseInt(document.getElementById('warehouse-height').value);
            warehouseConfig.cellSize = parseInt(document.getElementById('cell-size').value);
            
            renderWarehouse();
            updateStats();
        }
        
        // 渲染仓库网格
        function renderWarehouse() {
            const grid = document.getElementById('warehouse-grid');
            grid.innerHTML = '';
            
            // 设置网格样式
            grid.style.gridTemplateColumns = `repeat(${warehouseConfig.width}, ${warehouseConfig.cellSize}px)`;
            
            for (let y = 0; y < warehouseConfig.height; y++) {
                for (let x = 0; x < warehouseConfig.width; x++) {
                    const cell = document.createElement('div');
                    cell.className = 'cell';
                    cell.id = `cell-${x}-${y}`;
                    cell.style.width = `${warehouseConfig.cellSize}px`;
                    cell.style.height = `${warehouseConfig.cellSize}px`;
                    
                    // 设置点击事件
                    cell.onclick = () => handleCellClick(x, y);
                    
                    // 确定单元格类型
                    updateCellDisplay(cell, x, y);
                    
                    grid.appendChild(cell);
                }
            }
        }
        
        // 更新单元格显示
        function updateCellDisplay(cell, x, y) {
            if (x === warehouseConfig.startPoint.x && y === warehouseConfig.startPoint.y) {
                cell.className = 'cell start';
                cell.textContent = 'S';
            } else if (isObstacle(x, y)) {
                cell.className = 'cell obstacle';
                cell.textContent = '#';
            } else if (isShelfFace(x, y)) {
                cell.className = 'cell shelf-face';
                const location = getLocationAt(x, y);
                cell.textContent = location ? location.code : 'L';
            } else {
                cell.className = 'cell walkable';
                cell.textContent = '';
            }
        }
        
        // 处理单元格点击
        function handleCellClick(x, y) {
            console.log(`点击坐标: (${x}, ${y})`);
            // 可以在这里添加交互功能，比如手动设置起点或添加障碍物
        }
        
        // 添加货架
        function addShelf() {
            const width = parseInt(document.getElementById('shelf-width').value);
            const height = parseInt(document.getElementById('shelf-height').value);
            const spacing = parseInt(document.getElementById('shelf-spacing').value);
            
            // 自动计算货架位置
            const position = calculateNextShelfPosition(width, height, spacing);
            
            if (position) {
                shelfCounter++;
                const shelf = {
                    id: `SHELF_${shelfCounter}`,
                    name: `货架${shelfCounter}`,
                    x: position.x,
                    y: position.y,
                    width: width,
                    height: height
                };
                
                warehouseConfig.shelves.push(shelf);
                
                // 自动生成库位
                generateShelfLocations(shelf);
                
                renderWarehouse();
                updateShelfList();
                updateLocationList();
                updateStats();
            } else {
                alert('无法放置货架，空间不足！');
            }
        }
        
        // 计算下一个货架位置
        function calculateNextShelfPosition(width, height, spacing) {
            const maxX = warehouseConfig.width - width - 1;
            const maxY = warehouseConfig.height - height - 1;
            
            // 尝试按行放置货架
            for (let y = spacing; y <= maxY; y += height + spacing) {
                for (let x = spacing; x <= maxX; x += width + spacing) {
                    if (canPlaceShelf(x, y, width, height)) {
                        return {x, y};
                    }
                }
            }
            
            return null;
        }
        
        // 检查是否可以放置货架
        function canPlaceShelf(x, y, width, height) {
            // 检查是否与现有货架重叠
            for (const shelf of warehouseConfig.shelves) {
                if (!(x >= shelf.x + shelf.width || 
                      x + width <= shelf.x || 
                      y >= shelf.y + shelf.height || 
                      y + height <= shelf.y)) {
                    return false;
                }
            }
            
            // 检查是否与起点重叠
            if (x <= warehouseConfig.startPoint.x && 
                x + width > warehouseConfig.startPoint.x &&
                y <= warehouseConfig.startPoint.y && 
                y + height > warehouseConfig.startPoint.y) {
                return false;
            }
            
            return true;
        }
        
        // 生成货架库位 - 只在长边生成
        function generateShelfLocations(shelf) {
            const locations = [];
            const isWidthLonger = shelf.width >= shelf.height;
            
            if (isWidthLonger) {
                // 宽度更长，在上下两边生成库位
                
                // 上边库位
                if (shelf.y > 0) {
                    for (let x = shelf.x; x < shelf.x + shelf.width; x++) {
                        const locationId = `LOC_${shelf.id}_U${x - shelf.x + 1}`;
                        const locationCode = `${shelf.name}U${x - shelf.x + 1}`;
                        locations.push({
                            id: locationId,
                            code: locationCode,
                            x: x,
                            y: shelf.y - 1,
                            shelfId: shelf.id
                        });
                    }
                }
                
                // 下边库位
                if (shelf.y + shelf.height < warehouseConfig.height) {
                    for (let x = shelf.x; x < shelf.x + shelf.width; x++) {
                        const locationId = `LOC_${shelf.id}_D${x - shelf.x + 1}`;
                        const locationCode = `${shelf.name}D${x - shelf.x + 1}`;
                        locations.push({
                            id: locationId,
                            code: locationCode,
                            x: x,
                            y: shelf.y + shelf.height,
                            shelfId: shelf.id
                        });
                    }
                }
            } else {
                // 高度更长，在左右两边生成库位
                
                // 左边库位
                if (shelf.x > 0) {
                    for (let y = shelf.y; y < shelf.y + shelf.height; y++) {
                        const locationId = `LOC_${shelf.id}_L${y - shelf.y + 1}`;
                        const locationCode = `${shelf.name}L${y - shelf.y + 1}`;
                        locations.push({
                            id: locationId,
                            code: locationCode,
                            x: shelf.x - 1,
                            y: y,
                            shelfId: shelf.id
                        });
                    }
                }
                
                // 右边库位
                if (shelf.x + shelf.width < warehouseConfig.width) {
                    for (let y = shelf.y; y < shelf.y + shelf.height; y++) {
                        const locationId = `LOC_${shelf.id}_R${y - shelf.y + 1}`;
                        const locationCode = `${shelf.name}R${y - shelf.y + 1}`;
                        locations.push({
                            id: locationId,
                            code: locationCode,
                            x: shelf.x + shelf.width,
                            y: y,
                            shelfId: shelf.id
                        });
                    }
                }
            }
            
            warehouseConfig.locations.push(...locations);
        }
        
        // 清空货架
        function clearShelves() {
            warehouseConfig.shelves = [];
            warehouseConfig.locations = [];
            shelfCounter = 0;
            
            renderWarehouse();
            updateShelfList();
            updateLocationList();
            updateStats();
            clearPath();
        }
        
        // 删除货架
        function removeShelf(shelfId) {
            warehouseConfig.shelves = warehouseConfig.shelves.filter(shelf => shelf.id !== shelfId);
            warehouseConfig.locations = warehouseConfig.locations.filter(loc => loc.shelfId !== shelfId);
            
            renderWarehouse();
            updateShelfList();
            updateLocationList();
            updateStats();
            clearPath();
        }
        
        // 设置起点
        function setStartPoint() {
            const x = parseInt(document.getElementById('start-x').value);
            const y = parseInt(document.getElementById('start-y').value);
            
            if (x >= 0 && x < warehouseConfig.width && y >= 0 && y < warehouseConfig.height) {
                if (!isObstacle(x, y)) {
                    warehouseConfig.startPoint = {x, y};
                    renderWarehouse();
                } else {
                    alert('起点不能设置在货架上！');
                }
            } else {
                alert('起点坐标超出仓库范围！');
            }
        }
        
        // 计算路径
        function calculatePath() {
            const startTime = Date.now();
            const selectedLocations = Array.from(document.querySelectorAll('.location-checkbox:checked'))
                .map(cb => cb.value);
            
            if (selectedLocations.length === 0) {
                alert('请至少选择一个库位！');
                return;
            }
            
            // 获取库位坐标
            const locationPoints = selectedLocations.map(id => 
                warehouseConfig.locations.find(loc => loc.id === id)
            ).filter(loc => loc);
            
            // 优化访问顺序
            const optimizedOrder = optimizeLocationOrder(locationPoints, warehouseConfig.startPoint);
            
            // 构建完整路径
            const fullPath = [];
            let currentPoint = warehouseConfig.startPoint;
            
            for (const location of optimizedOrder) {
                const pathSegment = findPath(currentPoint, location);
                if (pathSegment.length > 0) {
                    if (fullPath.length > 0) {
                        pathSegment.shift(); // 移除重复的起点
                    }
                    fullPath.push(...pathSegment);
                    currentPoint = location;
                }
            }
            
            const endTime = Date.now();
            const computationTime = endTime - startTime;
            
            // 更新显示
            currentPath = fullPath;
            displayPath(fullPath, optimizedOrder);
            
            // 更新统计
            document.getElementById('path-distance').textContent = fullPath.length - 1;
            document.getElementById('computation-time').textContent = computationTime + 'ms';
        }
        
        // 显示路径
        function displayPath(path, targetLocations) {
            clearPath();
            
            path.forEach((point, index) => {
                const cell = document.getElementById(`cell-${point.x}-${point.y}`);
                if (cell && !cell.classList.contains('start')) {
                    if (targetLocations.some(loc => loc.x === point.x && loc.y === point.y)) {
                        cell.className = 'cell target';
                        const location = getLocationAt(point.x, point.y);
                        cell.textContent = location ? location.code.slice(-2) : 'T';
                    } else {
                        cell.className = 'cell path';
                        cell.textContent = '→';
                    }
                }
            });
        }
        
        // 清除路径
        function clearPath() {
            currentPath = [];
            
            for (let y = 0; y < warehouseConfig.height; y++) {
                for (let x = 0; x < warehouseConfig.width; x++) {
                    const cell = document.getElementById(`cell-${x}-${y}`);
                    if (cell && (cell.classList.contains('path') || cell.classList.contains('target'))) {
                        updateCellDisplay(cell, x, y);
                    }
                }
            }
            
            document.getElementById('path-distance').textContent = '0';
        }
        
        // 更新货架列表
        function updateShelfList() {
            const list = document.getElementById('shelf-list');
            list.innerHTML = '';
            
            warehouseConfig.shelves.forEach(shelf => {
                const item = document.createElement('div');
                item.className = 'shelf-item';
                item.innerHTML = `
                    <span>${shelf.name} (${shelf.width}x${shelf.height})</span>
                    <button onclick="removeShelf('${shelf.id}')">删除</button>
                `;
                list.appendChild(item);
            });
        }
        
        // 更新库位列表
        function updateLocationList() {
            const list = document.getElementById('location-list');
            list.innerHTML = '';
            
            warehouseConfig.locations.forEach(location => {
                const item = document.createElement('div');
                item.className = 'location-item';
                item.innerHTML = `
                    <input type="checkbox" class="location-checkbox" value="${location.id}">
                    <span>${location.code}</span>
                `;
                list.appendChild(item);
            });
        }
        
        // 更新统计信息
        function updateStats() {
            document.getElementById('total-shelves').textContent = warehouseConfig.shelves.length;
            document.getElementById('total-locations').textContent = warehouseConfig.locations.length;
        }
        
        // 辅助函数
        function isObstacle(x, y) {
            return warehouseConfig.shelves.some(shelf => 
                x >= shelf.x && x < shelf.x + shelf.width &&
                y >= shelf.y && y < shelf.y + shelf.height
            );
        }
        
        function isShelfFace(x, y) {
            return warehouseConfig.locations.some(loc => loc.x === x && loc.y === y);
        }
        
        function getLocationAt(x, y) {
            return warehouseConfig.locations.find(loc => loc.x === x && loc.y === y);
        }
        
        // A*路径查找算法
        function findPath(start, end) {
            const openSet = [{...start, g: 0, h: heuristic(start, end), f: heuristic(start, end), parent: null}];
            const closedSet = [];
            
            while (openSet.length > 0) {
                openSet.sort((a, b) => a.f - b.f);
                const current = openSet.shift();
                
                if (current.x === end.x && current.y === end.y) {
                    const path = [];
                    let node = current;
                    while (node) {
                        path.unshift({x: node.x, y: node.y});
                        node = node.parent;
                    }
                    return path;
                }
                
                closedSet.push(current);
                
                const neighbors = getNeighbors(current);
                for (const neighbor of neighbors) {
                    if (closedSet.some(node => node.x === neighbor.x && node.y === neighbor.y)) {
                        continue;
                    }
                    
                    const g = current.g + 1;
                    const h = heuristic(neighbor, end);
                    const f = g + h;
                    
                    const existingNode = openSet.find(node => node.x === neighbor.x && node.y === neighbor.y);
                    if (!existingNode) {
                        openSet.push({...neighbor, g, h, f, parent: current});
                    } else if (g < existingNode.g) {
                        existingNode.g = g;
                        existingNode.f = f;
                        existingNode.parent = current;
                    }
                }
            }
            
            return [];
        }
        
        function heuristic(a, b) {
            return Math.abs(a.x - b.x) + Math.abs(a.y - b.y);
        }
        
        function getNeighbors(node) {
            const neighbors = [];
            const directions = [{x: 0, y: 1}, {x: 1, y: 0}, {x: 0, y: -1}, {x: -1, y: 0}];
            
            for (const dir of directions) {
                const x = node.x + dir.x;
                const y = node.y + dir.y;
                
                if (x >= 0 && x < warehouseConfig.width && 
                    y >= 0 && y < warehouseConfig.height && 
                    !isObstacle(x, y)) {
                    neighbors.push({x, y});
                }
            }
            
            return neighbors;
        }
        
        function optimizeLocationOrder(locations, start) {
            if (locations.length <= 1) return locations;
            
            const unvisited = [...locations];
            const visited = [];
            let current = start;
            
            while (unvisited.length > 0) {
                let nearest = unvisited[0];
                let minDistance = heuristic(current, nearest);
                
                for (const location of unvisited) {
                    const distance = heuristic(current, location);
                    if (distance < minDistance) {
                        minDistance = distance;
                        nearest = location;
                    }
                }
                
                visited.push(nearest);
                unvisited.splice(unvisited.indexOf(nearest), 1);
                current = nearest;
            }
            
            return visited;
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeWarehouse();
        });
    </script>
</body>
</html>
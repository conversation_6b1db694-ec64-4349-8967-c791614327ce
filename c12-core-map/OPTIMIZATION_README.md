# C12 Core Map - 优化路径规划服务

## 概述

新增的优化路径规划服务提供两个核心功能：

1. **方法1：路径规划优化** - 基于线路图的最短路径规划和库位排序
2. **方法2：货架切割和库位生成** - 根据货架尺寸生成库位编号和坐标

## 🎯 核心功能

### 方法1：路径规划优化

**功能描述：**
- 输入：库位列表（code + 4个角点坐标）+ 线路图（多个线路段）+ 起始点
- 输出：优化的库位访问顺序 + 完整的路径坐标

**特点：**
- ✅ 自动在线路上找到库位的锚点位置
- ✅ 使用贪心算法优化访问顺序（TSP问题的近似解）
- ✅ 支持重合点位的处理
- ✅ 返回完整的路径轨迹坐标

### 方法2：货架切割和库位生成

**功能描述：**
- 输入：货架长宽高 + 底部4个角点 + 切割行列数 + 编号规则
- 输出：所有库位的编号和4个角点坐标

**特点：**
- ✅ 支持任意角度的货架（不限于水平/垂直）
- ✅ 灵活的编号规则（支持模式替换）
- ✅ 精确的几何计算
- ✅ 批量生成多个货架

## 🚀 快速开始

### 1. 基本使用

```java
import com.chinaservices.wms.map.optimization.*;

// 创建服务实例
OptimizedPathPlanningService service = new OptimizedPathPlanningService();
```

### 2. 方法1：路径规划优化

```java
// 准备库位数据（code + 4个角点坐标）
List<StorageLocation> locations = Arrays.asList(
    new StorageLocation("A01", Arrays.asList(
        new Point(2, 2), new Point(3, 2), new Point(3, 3), new Point(2, 3)
    )),
    new StorageLocation("B02", Arrays.asList(
        new Point(5, 1), new Point(7, 1), new Point(7, 2), new Point(5, 2)
    ))
);

// 准备线路图（多个线路段）
List<RouteSegment> routes = Arrays.asList(
    new RouteSegment(Arrays.asList(
        new Point(0, 0), new Point(2, 0), new Point(4, 0), new Point(6, 0)
    )),
    new RouteSegment(Arrays.asList(
        new Point(6, 0), new Point(6, 2), new Point(6, 4)
    ))
);

// 设置起始点
Point startPoint = new Point(0, 0);

// 执行路径规划
PathPlanningResult result = service.optimizePathPlanning(locations, routes, startPoint);

// 获取结果
List<String> locationOrder = result.getOptimizedLocationCodes(); // 库位访问顺序
List<String> pathCoordinates = result.getPathCoordinates();      // 路径坐标
```

### 3. 方法2：货架切割和库位生成

```java
// 设置货架参数
double length = 10.0;  // 长度
double width = 5.0;    // 宽度  
double height = 3.0;   // 高度

// 货架底部4个角点（矩形）
List<Point> bottomCorners = Arrays.asList(
    new Point(0, 0),    // 左下角
    new Point(10, 0),   // 右下角
    new Point(10, 5),   // 右上角
    new Point(0, 5)     // 左上角
);

// 切割参数
int columns = 5;  // 5列
int rows = 2;     // 2行
String codePattern = "A{row:02d}{col:02d}";  // 编号规则：A0101, A0102...

// 生成库位
List<ShelfLocationResult> locations = service.generateShelfLocations(
    length, width, height, bottomCorners, columns, rows, codePattern);

// 获取结果
for (ShelfLocationResult location : locations) {
    String code = location.getLocationCode();           // 库位编号
    List<Point> corners = location.getCorners();        // 4个角点坐标
}
```

## 🛠️ 工具类使用

为了简化使用，提供了 `PathPlanningUtils` 工具类：

### 简化的路径规划

```java
// 使用字符串坐标格式
Map<String, List<String>> locationData = new HashMap<>();
locationData.put("A01", Arrays.asList("2,2", "3,2", "3,3", "2,3"));
locationData.put("B02", Arrays.asList("5,1", "7,1", "7,2", "5,2"));

List<List<String>> routeData = Arrays.asList(
    Arrays.asList("0,0", "2,0", "4,0", "6,0"),
    Arrays.asList("6,0", "6,2", "6,4")
);

String startCoordinate = "0,0";

// 一行代码完成路径规划
PathPlanningResult result = PathPlanningUtils.planPath(locationData, routeData, startCoordinate);
```

### 简化的货架生成

```java
// 使用字符串坐标格式
List<String> bottomCorners = Arrays.asList("0,0", "10,0", "10,5", "0,5");

// 一行代码完成货架生成
List<ShelfLocationResult> results = PathPlanningUtils.generateShelf(
    10.0, 5.0, 3.0, bottomCorners, 5, 2, "A{row:02d}{col:02d}");
```

## 📊 返回值说明

### PathPlanningResult

| 字段 | 类型 | 说明 |
|------|------|------|
| `optimizedLocationCodes` | `List<String>` | 优化后的库位code排序 |
| `pathCoordinates` | `List<String>` | 路径坐标字符串数组 |
| `totalDistance` | `double` | 路径总距离 |
| `computationTime` | `long` | 计算耗时(ms) |

### ShelfLocationResult

| 字段 | 类型 | 说明 |
|------|------|------|
| `locationCode` | `String` | 库位编号 |
| `corners` | `List<Point>` | 4个角点坐标 |

## 🔧 编号规则

支持以下模式替换：

- `{row}` - 行号（从1开始）
- `{col}` - 列号（从1开始）
- `{row:02d}` - 2位数行号（01, 02, ...）
- `{col:02d}` - 2位数列号（01, 02, ...）

**示例：**
- `"A{row:02d}{col:02d}"` → `A0101`, `A0102`, `A0201`, ...
- `"SHELF_{row}_{col}"` → `SHELF_1_1`, `SHELF_1_2`, `SHELF_2_1`, ...

## 🧪 测试和验证

### 运行测试

```bash
# 编译项目
mvn compile

# 运行快速测试
java -cp target/classes com.chinaservices.wms.map.optimization.QuickTest

# 运行详细示例
java -cp target/classes com.chinaservices.wms.map.optimization.OptimizedPathPlanningExample
```

### 数据验证

```java
// 验证库位数据格式
boolean isValid = PathPlanningUtils.validateLocationData(locationData);

// 验证线路数据格式
boolean isValid = PathPlanningUtils.validateRouteData(routeData);
```

## 📈 性能特点

- **时间复杂度**: O(n²) 对于n个库位的路径规划
- **空间复杂度**: O(n + m) 对于n个库位和m个路径点
- **适用规模**: 支持100+库位的实时规划
- **计算速度**: 通常在100ms内完成中等规模的规划

## 🔍 算法说明

### 路径规划算法

1. **锚点查找**: 为每个库位在线路上找到最近的锚点
2. **顺序优化**: 使用贪心算法优化访问顺序（最近邻启发式）
3. **路径构建**: 在线路图上构建完整的移动轨迹
4. **距离计算**: 计算欧几里得距离作为路径成本

### 货架切割算法

1. **向量计算**: 根据4个角点计算长度和宽度方向向量
2. **网格划分**: 按行列数均匀划分货架区域
3. **坐标计算**: 为每个库位计算精确的4个角点坐标
4. **编号生成**: 根据模式规则生成库位编号

## 🚨 注意事项

1. **坐标格式**: 坐标字符串必须是 "x,y" 格式，如 "1.5,2.3"
2. **库位角点**: 必须提供4个角点，建议按逆时针顺序
3. **线路连通性**: 确保线路段之间是连通的
4. **重合处理**: 算法会自动处理重合的点位
5. **性能考虑**: 大规模数据建议分批处理

## 📞 支持

如有问题或需要优化建议，请联系开发团队。

package com.chinaservices.wms.map;

import com.chinaservices.wms.map.grid.GridPoint;
import com.chinaservices.wms.map.grid.WarehouseGrid;
import com.chinaservices.wms.map.thirdparty.JGraphTPathService;

import java.util.*;

/**
 * 使用第三方库的路径规划演示
 * 展示JGraphT等成熟库的使用
 * 
 * <AUTHOR> Agent
 */
public class ThirdPartyExample {
    
    public static void main(String[] args) {
        System.out.println("=== C12 Core Map - 第三方库路径规划演示 ===\n");
        
        // 1. 创建仓库网格
        System.out.println("1. 创建仓库网格...");
        WarehouseGrid grid = createTestWarehouse();
        printWarehouseInfo(grid);
        
        // 2. 创建JGraphT路径服务
        System.out.println("\n2. 初始化JGraphT路径服务...");
        JGraphTPathService pathService = new JGraphTPathService(grid);
        printServiceInfo(pathService);
        
        // 3. 测试路径规划
        System.out.println("\n3. 测试路径规划...");
        testPathPlanning(pathService, grid);
        
        // 4. 性能对比
        System.out.println("\n4. 性能测试...");
        performanceTest(pathService, grid);
        
        System.out.println("\n=== 演示完成 ===");
        System.out.println("\n💡 建议: 在生产环境中使用JGraphT等成熟的第三方库");
        System.out.println("   - 算法经过充分验证");
        System.out.println("   - 性能优化良好");
        System.out.println("   - 社区支持活跃");
        System.out.println("   - 减少维护成本");
    }
    
    /**
     * 创建测试仓库
     */
    private static WarehouseGrid createTestWarehouse() {
        WarehouseGrid grid = new WarehouseGrid(25, 20);
        
        // 设置起始点
        grid.setStartPoint(new GridPoint(2, 18));
        
        // 添加货架A
        grid.addRectangleShelf("SHELF_A", 5, 3, 6, 8);
        grid.addShelfFace("SHELF_A", 4, 5, "LOC_A01", "A01");
        grid.addShelfFace("SHELF_A", 4, 7, "LOC_A02", "A02");
        grid.addShelfFace("SHELF_A", 11, 6, "LOC_A03", "A03");
        
        // 添加货架B
        grid.addRectangleShelf("SHELF_B", 15, 3, 6, 8);
        grid.addShelfFace("SHELF_B", 14, 5, "LOC_B01", "B01");
        grid.addShelfFace("SHELF_B", 14, 7, "LOC_B02", "B02");
        grid.addShelfFace("SHELF_B", 21, 6, "LOC_B03", "B03");
        
        // 添加货架C
        grid.addRectangleShelf("SHELF_C", 10, 13, 8, 4);
        grid.addShelfFace("SHELF_C", 12, 12, "LOC_C01", "C01");
        grid.addShelfFace("SHELF_C", 16, 12, "LOC_C02", "C02");
        
        return grid;
    }
    
    /**
     * 打印仓库信息
     */
    private static void printWarehouseInfo(WarehouseGrid grid) {
        Map<String, Integer> stats = grid.getStatistics();
        System.out.println("仓库信息:");
        System.out.println("  尺寸: " + grid.getWidth() + "x" + grid.getHeight());
        System.out.println("  可行走区域: " + stats.get("walkable"));
        System.out.println("  货架障碍物: " + stats.get("obstacle"));
        System.out.println("  库位数量: " + stats.get("shelfLocations"));
        
        // 打印布局示例
        System.out.println("\n仓库布局:");
        for (int y = 0; y < Math.min(15, grid.getHeight()); y++) {
            System.out.print("  ");
            for (int x = 0; x < grid.getWidth(); x++) {
                char c;
                GridPoint point = new GridPoint(x, y);
                
                if (point.equals(grid.getStartPoint())) {
                    c = 'S';
                } else {
                    switch (grid.getCellType(x, y)) {
                        case WALKABLE: c = '.'; break;
                        case OBSTACLE: c = '#'; break;
                        case SHELF_FACE: c = 'L'; break;
                        default: c = '?'; break;
                    }
                }
                System.out.print(c);
            }
            System.out.println();
        }
        System.out.println("  图例: S=起点, .=通道, #=货架, L=库位");
    }
    
    /**
     * 打印服务信息
     */
    private static void printServiceInfo(JGraphTPathService pathService) {
        Map<String, Object> stats = pathService.getGraphStatistics();
        System.out.println("JGraphT服务信息:");
        System.out.println("  算法库: " + stats.get("algorithm"));
        System.out.println("  图顶点数: " + stats.get("vertices"));
        System.out.println("  图边数: " + stats.get("edges"));
        System.out.println("  算法: Dijkstra + 2-Opt TSP");
    }
    
    /**
     * 测试路径规划
     */
    private static void testPathPlanning(JGraphTPathService pathService, WarehouseGrid grid) {
        // 创建拣货任务
        List<String> locationIds = Arrays.asList("LOC_A01", "LOC_B02", "LOC_C01", "LOC_A03");
        GridPoint startPoint = grid.getStartPoint();
        
        System.out.println("拣货任务:");
        System.out.println("  起始点: " + startPoint);
        System.out.println("  目标库位: " + locationIds);
        
        // 计算路径
        JGraphTPathService.PathPlanningResult result = 
            pathService.calculateOptimalPath(locationIds, startPoint);
        
        System.out.println("\n路径规划结果:");
        System.out.println("  " + result);
        
        if (result.isSuccess()) {
            // 显示优化后的库位访问顺序
            System.out.println("  优化后的库位访问顺序:");
            for (int i = 0; i < result.getOptimizedLocationOrder().size(); i++) {
                GridPoint point = result.getOptimizedLocationOrder().get(i);
                WarehouseGrid.ShelfLocation location = grid.getShelfLocation(point);
                String locationCode = location != null ? location.getLocationCode() : "Unknown";
                System.out.println("    " + (i + 1) + ". " + locationCode + " at " + point);
            }

            // 显示完整的路径轨迹
            System.out.println("  完整路径轨迹长度: " + result.getPathTrajectory().size() + " 步");

            if (result.getPathTrajectory().size() <= 20) {
                System.out.println("  详细路径轨迹: " + result.getPathTrajectory());
            } else {
                // 显示关键轨迹点
                List<GridPoint> trajectory = result.getPathTrajectory();
                System.out.println("  路径轨迹关键点:");
                System.out.println("    起点: " + trajectory.get(0));

                // 显示每个库位的到达点
                for (GridPoint locationPoint : result.getOptimizedLocationOrder()) {
                    int index = trajectory.indexOf(locationPoint);
                    if (index >= 0) {
                        System.out.println("    到达库位 " + locationPoint + " (第" + (index + 1) + "步)");
                    }
                }

                System.out.println("    终点: " + trajectory.get(trajectory.size() - 1));
            }
        }
    }
    
    /**
     * 性能测试
     */
    private static void performanceTest(JGraphTPathService pathService, WarehouseGrid grid) {
        System.out.println("JGraphT性能测试:");
        
        // 测试不同规模的任务
        String[][] testCases = {
            {"LOC_A01", "LOC_B01"},
            {"LOC_A01", "LOC_B02", "LOC_C01"},
            {"LOC_A01", "LOC_A02", "LOC_B01", "LOC_B02"},
            {"LOC_A01", "LOC_A02", "LOC_A03", "LOC_B01", "LOC_B02", "LOC_C01", "LOC_C02"}
        };
        
        GridPoint startPoint = grid.getStartPoint();
        
        for (int i = 0; i < testCases.length; i++) {
            List<String> locationIds = Arrays.asList(testCases[i]);
            
            JGraphTPathService.PathPlanningResult result = 
                pathService.calculateOptimalPath(locationIds, startPoint);
            
            System.out.println("  " + (locationIds.size()) + " 个库位: " +
                             (result.isSuccess() ?
                              String.format("距离=%.2f, 轨迹=%d步, 耗时=%dms",
                                          result.getTotalDistance(),
                                          result.getPathTrajectory().size(),
                                          result.getComputationTime()) :
                              "失败"));
        }
        
        // 单点路径测试
        System.out.println("\n单点路径测试:");
        GridPoint from = new GridPoint(2, 18);
        GridPoint to = new GridPoint(21, 6);
        
        long startTime = System.currentTimeMillis();
        List<GridPoint> path = pathService.getShortestPath(from, to);
        long endTime = System.currentTimeMillis();
        
        System.out.println("  " + from + " → " + to + ": " +
                         (path.isEmpty() ? "无路径" : 
                          String.format("距离=%d步, 耗时=%dms", path.size(), endTime - startTime)));
    }
    
    /**
     * 展示第三方库的优势
     */
    public static void showThirdPartyAdvantages() {
        System.out.println("\n🔧 第三方库优势对比:");
        
        System.out.println("\n📚 JGraphT:");
        System.out.println("  ✅ 成熟稳定 - 15年+开发历史");
        System.out.println("  ✅ 算法丰富 - 50+图算法");
        System.out.println("  ✅ 性能优化 - 工业级优化");
        System.out.println("  ✅ 社区支持 - 活跃的开发社区");
        
        System.out.println("\n🚀 GraphHopper:");
        System.out.println("  ✅ 专业路径规划 - 地图导航级别");
        System.out.println("  ✅ 大规模支持 - 百万级节点");
        System.out.println("  ✅ 实时计算 - 毫秒级响应");
        System.out.println("  ✅ 商业支持 - 企业级服务");
        
        System.out.println("\n📊 Apache Commons Math:");
        System.out.println("  ✅ Apache基金会 - 可靠性保证");
        System.out.println("  ✅ 数学优化 - 专业数值计算");
        System.out.println("  ✅ 广泛使用 - 大量项目验证");
        System.out.println("  ✅ 长期维护 - 持续更新支持");
        
        System.out.println("\n⚠️  自研算法风险:");
        System.out.println("  ❌ 算法bug - 边界情况处理");
        System.out.println("  ❌ 性能问题 - 缺乏深度优化");
        System.out.println("  ❌ 维护成本 - 需要专业团队");
        System.out.println("  ❌ 测试覆盖 - 难以穷尽测试");
    }
}

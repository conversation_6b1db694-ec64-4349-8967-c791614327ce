package com.chinaservices.wms.map.thirdparty;

import com.chinaservices.wms.map.grid.GridPoint;
import com.chinaservices.wms.map.grid.WarehouseGrid;
import org.jgrapht.Graph;
import org.jgrapht.GraphPath;
import org.jgrapht.alg.shortestpath.DijkstraShortestPath;
import org.jgrapht.alg.tour.TwoOptHeuristicTSP;
import org.jgrapht.graph.DefaultWeightedEdge;
import org.jgrapht.graph.SimpleWeightedGraph;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 基于JGraphT的专业路径规划服务
 * 使用成熟的第三方库确保算法的正确性和性能
 * 
 * <AUTHOR> Agent
 */
public class JGraphTPathService {
    
    /**
     * 路径规划结果
     */
    public static class PathPlanningResult {
        private List<GridPoint> optimizedLocationOrder;  // 优化后的库位访问顺序
        private List<GridPoint> pathTrajectory;          // 完整的路径轨迹点位
        private double totalDistance;
        private long computationTime;
        private boolean success;
        private String algorithm;

        public PathPlanningResult() {
            this.optimizedLocationOrder = new ArrayList<>();
            this.pathTrajectory = new ArrayList<>();
        }

        // Getters and setters
        public List<GridPoint> getOptimizedLocationOrder() { return optimizedLocationOrder; }
        public void setOptimizedLocationOrder(List<GridPoint> optimizedLocationOrder) {
            this.optimizedLocationOrder = optimizedLocationOrder;
        }

        public List<GridPoint> getPathTrajectory() { return pathTrajectory; }
        public void setPathTrajectory(List<GridPoint> pathTrajectory) {
            this.pathTrajectory = pathTrajectory;
        }

        public double getTotalDistance() { return totalDistance; }
        public void setTotalDistance(double totalDistance) { this.totalDistance = totalDistance; }
        public long getComputationTime() { return computationTime; }
        public void setComputationTime(long computationTime) { this.computationTime = computationTime; }
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        public String getAlgorithm() { return algorithm; }
        public void setAlgorithm(String algorithm) { this.algorithm = algorithm; }

        @Override
        public String toString() {
            return String.format("PathPlanningResult[success=%s, distance=%.2f, locations=%d, trajectory=%d, algorithm=%s, time=%dms]",
                               success, totalDistance, optimizedLocationOrder.size(), pathTrajectory.size(), algorithm, computationTime);
        }
    }
    
    private WarehouseGrid warehouseGrid;
    private Graph<GridPoint, DefaultWeightedEdge> graph;
    private DijkstraShortestPath<GridPoint, DefaultWeightedEdge> dijkstraAlgorithm;
    
    public JGraphTPathService(WarehouseGrid warehouseGrid) {
        this.warehouseGrid = warehouseGrid;
        this.graph = buildGraph();
        this.dijkstraAlgorithm = new DijkstraShortestPath<>(graph);
    }
    
    /**
     * 计算最佳拣货路径
     */
    public PathPlanningResult calculateOptimalPath(List<String> locationIds, GridPoint startPoint) {
        long startTime = System.currentTimeMillis();
        PathPlanningResult result = new PathPlanningResult();
        result.setAlgorithm("JGraphT Dijkstra + TSP");
        
        try {
            // 1. 获取有效的库位点
            List<GridPoint> targetPoints = getValidLocationPoints(locationIds);
            if (targetPoints.isEmpty()) {
                result.setSuccess(false);
                return result;
            }
            
            // 2. 使用JGraphT的TSP算法优化访问顺序
            List<GridPoint> optimizedOrder = optimizeVisitOrder(targetPoints, startPoint);
            result.setOptimizedLocationOrder(optimizedOrder);

            // 3. 使用Dijkstra算法构建完整的路径轨迹
            List<GridPoint> pathTrajectory = buildCompletePathTrajectory(optimizedOrder, startPoint);
            result.setPathTrajectory(pathTrajectory);

            // 4. 计算总距离
            double totalDistance = calculatePathDistance(pathTrajectory);
            result.setTotalDistance(totalDistance);
            
            result.setSuccess(true);
            
        } catch (Exception e) {
            result.setSuccess(false);
            System.err.println("路径计算失败: " + e.getMessage());
        } finally {
            result.setComputationTime(System.currentTimeMillis() - startTime);
        }
        
        return result;
    }
    
    /**
     * 构建JGraphT图结构
     */
    private Graph<GridPoint, DefaultWeightedEdge> buildGraph() {
        Graph<GridPoint, DefaultWeightedEdge> graph = new SimpleWeightedGraph<>(DefaultWeightedEdge.class);
        
        // 添加所有可访问的点作为顶点
        for (int x = 0; x < warehouseGrid.getWidth(); x++) {
            for (int y = 0; y < warehouseGrid.getHeight(); y++) {
                GridPoint point = new GridPoint(x, y);
                if (warehouseGrid.isAccessible(point)) {
                    graph.addVertex(point);
                }
            }
        }
        
        // 添加边（相邻可访问点之间的连接）
        for (GridPoint vertex : graph.vertexSet()) {
            for (GridPoint neighbor : vertex.getNeighbors()) {
                if (graph.containsVertex(neighbor)) {
                    if (!graph.containsEdge(vertex, neighbor)) {
                        DefaultWeightedEdge edge = graph.addEdge(vertex, neighbor);
                        if (edge != null) {
                            // 设置边的权重为欧几里得距离
                            double weight = vertex.euclideanDistance(neighbor);
                            graph.setEdgeWeight(edge, weight);
                        }
                    }
                }
            }
        }
        
        System.out.println("构建图完成: " + graph.vertexSet().size() + " 个顶点, " + 
                         graph.edgeSet().size() + " 条边");
        
        return graph;
    }
    
    /**
     * 获取有效的库位点
     */
    private List<GridPoint> getValidLocationPoints(List<String> locationIds) {
        List<GridPoint> points = new ArrayList<>();
        
        for (String locationId : locationIds) {
            WarehouseGrid.ShelfLocation location = warehouseGrid.findShelfLocationById(locationId);
            if (location != null) {
                GridPoint point = location.getGridPoint();
                if (graph.containsVertex(point)) {
                    points.add(point);
                }
            }
        }
        
        return points;
    }
    
    /**
     * 使用JGraphT的TSP算法优化访问顺序
     */
    private List<GridPoint> optimizeVisitOrder(List<GridPoint> targetPoints, GridPoint startPoint) {
        if (targetPoints.size() <= 1) {
            return new ArrayList<>(targetPoints);
        }
        
        // 创建包含目标点的子图
        Graph<GridPoint, DefaultWeightedEdge> tspGraph = new SimpleWeightedGraph<>(DefaultWeightedEdge.class);
        
        // 添加所有目标点
        for (GridPoint point : targetPoints) {
            tspGraph.addVertex(point);
        }
        
        // 添加起始点（如果不在目标点中）
        if (!targetPoints.contains(startPoint)) {
            tspGraph.addVertex(startPoint);
        }
        
        // 计算所有点对之间的最短距离并添加边
        for (GridPoint from : tspGraph.vertexSet()) {
            for (GridPoint to : tspGraph.vertexSet()) {
                if (!from.equals(to) && !tspGraph.containsEdge(from, to)) {
                    GraphPath<GridPoint, DefaultWeightedEdge> path = dijkstraAlgorithm.getPath(from, to);
                    if (path != null) {
                        DefaultWeightedEdge edge = tspGraph.addEdge(from, to);
                        if (edge != null) {
                            tspGraph.setEdgeWeight(edge, path.getWeight());
                        }
                    }
                }
            }
        }
        
        // 使用JGraphT的2-opt TSP算法
        TwoOptHeuristicTSP<GridPoint, DefaultWeightedEdge> tspSolver = new TwoOptHeuristicTSP<>();
        GraphPath<GridPoint, DefaultWeightedEdge> tspTour = tspSolver.getTour(tspGraph);
        
        // 提取访问顺序（排除起始点）
        List<GridPoint> optimizedOrder = new ArrayList<>();
        List<GridPoint> tourVertices = tspTour.getVertexList();
        
        // 找到起始点在tour中的位置
        int startIndex = tourVertices.indexOf(startPoint);
        if (startIndex == -1) {
            startIndex = 0;
        }
        
        // 从起始点开始重新排列，只包含目标点
        for (int i = 1; i < tourVertices.size(); i++) {
            int index = (startIndex + i) % tourVertices.size();
            GridPoint point = tourVertices.get(index);
            if (targetPoints.contains(point)) {
                optimizedOrder.add(point);
            }
        }
        
        return optimizedOrder;
    }
    
    /**
     * 构建完整的路径轨迹（包含每一步的移动轨迹）
     */
    private List<GridPoint> buildCompletePathTrajectory(List<GridPoint> optimizedOrder, GridPoint startPoint) {
        List<GridPoint> pathTrajectory = new ArrayList<>();
        GridPoint currentPoint = startPoint;

        // 添加起始点
        pathTrajectory.add(startPoint);

        // 为每个库位构建详细路径
        for (GridPoint targetPoint : optimizedOrder) {
            GraphPath<GridPoint, DefaultWeightedEdge> path = dijkstraAlgorithm.getPath(currentPoint, targetPoint);
            if (path != null) {
                List<GridPoint> pathVertices = path.getVertexList();

                // 添加路径上的每一个点（跳过起点避免重复）
                for (int i = 1; i < pathVertices.size(); i++) {
                    pathTrajectory.add(pathVertices.get(i));
                }

                currentPoint = targetPoint;
            } else {
                // 如果找不到路径，直接连接（应该不会发生，但作为备用）
                System.err.println("警告: 无法找到从 " + currentPoint + " 到 " + targetPoint + " 的路径");
                pathTrajectory.add(targetPoint);
                currentPoint = targetPoint;
            }
        }

        return pathTrajectory;
    }
    
    /**
     * 计算路径总距离
     */
    private double calculatePathDistance(List<GridPoint> path) {
        if (path.size() <= 1) return 0.0;
        
        double totalDistance = 0.0;
        for (int i = 0; i < path.size() - 1; i++) {
            totalDistance += path.get(i).euclideanDistance(path.get(i + 1));
        }
        
        return totalDistance;
    }
    
    /**
     * 获取两点间的最短路径
     */
    public List<GridPoint> getShortestPath(GridPoint from, GridPoint to) {
        GraphPath<GridPoint, DefaultWeightedEdge> path = dijkstraAlgorithm.getPath(from, to);
        return path != null ? path.getVertexList() : new ArrayList<>();
    }
    
    /**
     * 获取图统计信息
     */
    public Map<String, Object> getGraphStatistics() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("vertices", graph.vertexSet().size());
        stats.put("edges", graph.edgeSet().size());
        stats.put("algorithm", "JGraphT");
        return stats;
    }
}

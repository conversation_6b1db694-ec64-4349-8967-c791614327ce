package com.chinaservices.wms.map.grid;

import java.util.*;

/**
 * 仓库网格 - 表示整个仓库的网格化布局
 * 
 * <AUTHOR> Agent
 */
public class WarehouseGrid {
    
    /**
     * 网格类型枚举
     */
    public enum CellType {
        WALKABLE,    // 可行走区域（通道）
        OBSTACLE,    // 障碍物（货架）
        SHELF_FACE,  // 货架面（可访问的库位）
        BOUNDARY     // 边界
    }
    
    /**
     * 网格宽度
     */
    private int width;
    
    /**
     * 网格高度
     */
    private int height;
    
    /**
     * 网格数据 - 存储每个格子的类型
     */
    private CellType[][] grid;
    
    /**
     * 货架信息映射 - 存储每个货架面格子对应的货架ID和库位信息
     */
    private Map<GridPoint, ShelfLocation> shelfLocations;
    
    /**
     * 起始点
     */
    private GridPoint startPoint;
    
    /**
     * 结束点
     */
    private GridPoint endPoint;
    
    /**
     * 货架面库位信息
     */
    public static class ShelfLocation {
        private String shelfId;
        private String locationId;
        private String locationCode;
        private GridPoint gridPoint;
        
        public ShelfLocation(String shelfId, String locationId, String locationCode, GridPoint gridPoint) {
            this.shelfId = shelfId;
            this.locationId = locationId;
            this.locationCode = locationCode;
            this.gridPoint = gridPoint;
        }
        
        public String getShelfId() { return shelfId; }
        public String getLocationId() { return locationId; }
        public String getLocationCode() { return locationCode; }
        public GridPoint getGridPoint() { return gridPoint; }
        
        @Override
        public String toString() {
            return String.format("%s[%s] at %s", locationCode, locationId, gridPoint);
        }
    }
    
    public WarehouseGrid(int width, int height) {
        this.width = width;
        this.height = height;
        this.grid = new CellType[height][width];
        this.shelfLocations = new HashMap<>();
        
        // 初始化为可行走区域
        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                grid[y][x] = CellType.WALKABLE;
            }
        }
    }
    
    public int getWidth() { return width; }
    public int getHeight() { return height; }
    public GridPoint getStartPoint() { return startPoint; }
    public void setStartPoint(GridPoint startPoint) { this.startPoint = startPoint; }
    public GridPoint getEndPoint() { return endPoint; }
    public void setEndPoint(GridPoint endPoint) { this.endPoint = endPoint; }
    
    /**
     * 获取指定位置的网格类型
     */
    public CellType getCellType(int x, int y) {
        if (x < 0 || x >= width || y < 0 || y >= height) {
            return CellType.BOUNDARY;
        }
        return grid[y][x];
    }
    
    public CellType getCellType(GridPoint point) {
        return getCellType(point.getX(), point.getY());
    }
    
    /**
     * 设置指定位置的网格类型
     */
    public void setCellType(int x, int y, CellType type) {
        if (x >= 0 && x < width && y >= 0 && y < height) {
            grid[y][x] = type;
        }
    }
    
    public void setCellType(GridPoint point, CellType type) {
        setCellType(point.getX(), point.getY(), type);
    }
    
    /**
     * 检查指定位置是否可行走
     */
    public boolean isWalkable(int x, int y) {
        CellType type = getCellType(x, y);
        return type == CellType.WALKABLE;
    }
    
    public boolean isWalkable(GridPoint point) {
        return isWalkable(point.getX(), point.getY());
    }
    
    /**
     * 检查指定位置是否可访问（可行走或货架面）
     */
    public boolean isAccessible(GridPoint point) {
        CellType type = getCellType(point);
        return type == CellType.WALKABLE || type == CellType.SHELF_FACE;
    }
    
    /**
     * 添加矩形货架
     */
    public void addRectangleShelf(String shelfId, int startX, int startY, int width, int height) {
        for (int y = startY; y < startY + height; y++) {
            for (int x = startX; x < startX + width; x++) {
                setCellType(x, y, CellType.OBSTACLE);
            }
        }
    }
    
    /**
     * 添加货架面（可访问的库位）
     */
    public void addShelfFace(String shelfId, int x, int y, String locationId, String locationCode) {
        GridPoint point = new GridPoint(x, y);
        setCellType(point, CellType.SHELF_FACE);
        shelfLocations.put(point, new ShelfLocation(shelfId, locationId, locationCode, point));
    }
    
    /**
     * 批量添加货架面
     */
    public void addShelfFaces(String shelfId, List<ShelfFaceData> faces) {
        for (ShelfFaceData face : faces) {
            addShelfFace(shelfId, face.x, face.y, face.locationId, face.locationCode);
        }
    }
    
    /**
     * 货架面数据
     */
    public static class ShelfFaceData {
        public int x, y;
        public String locationId, locationCode;
        
        public ShelfFaceData(int x, int y, String locationId, String locationCode) {
            this.x = x;
            this.y = y;
            this.locationId = locationId;
            this.locationCode = locationCode;
        }
    }
    
    /**
     * 获取货架面库位信息
     */
    public ShelfLocation getShelfLocation(GridPoint point) {
        return shelfLocations.get(point);
    }
    
    /**
     * 获取所有货架面库位
     */
    public Collection<ShelfLocation> getAllShelfLocations() {
        return shelfLocations.values();
    }
    
    /**
     * 根据库位ID查找货架面
     */
    public ShelfLocation findShelfLocationById(String locationId) {
        return shelfLocations.values().stream()
                           .filter(loc -> loc.getLocationId().equals(locationId))
                           .findFirst()
                           .orElse(null);
    }
    
    /**
     * 获取指定点的可行走邻居
     */
    public List<GridPoint> getWalkableNeighbors(GridPoint point) {
        List<GridPoint> neighbors = new ArrayList<>();
        
        for (GridPoint neighbor : point.getNeighbors()) {
            if (isWalkable(neighbor)) {
                neighbors.add(neighbor);
            }
        }
        
        return neighbors;
    }
    
    /**
     * 获取指定点的可访问邻居（包括货架面）
     */
    public List<GridPoint> getAccessibleNeighbors(GridPoint point) {
        List<GridPoint> neighbors = new ArrayList<>();
        
        for (GridPoint neighbor : point.getNeighbors()) {
            if (isAccessible(neighbor)) {
                neighbors.add(neighbor);
            }
        }
        
        return neighbors;
    }
    
    /**
     * 找到最近的可行走点（用于货架面到通道的连接）
     */
    public GridPoint findNearestWalkablePoint(GridPoint shelfPoint) {
        Queue<GridPoint> queue = new LinkedList<>();
        Set<GridPoint> visited = new HashSet<>();
        
        queue.offer(shelfPoint);
        visited.add(shelfPoint);
        
        while (!queue.isEmpty()) {
            GridPoint current = queue.poll();
            
            if (isWalkable(current)) {
                return current;
            }
            
            for (GridPoint neighbor : current.getNeighbors()) {
                if (!visited.contains(neighbor) && 
                    neighbor.getX() >= 0 && neighbor.getX() < width &&
                    neighbor.getY() >= 0 && neighbor.getY() < height) {
                    visited.add(neighbor);
                    queue.offer(neighbor);
                }
            }
        }
        
        return null; // 找不到可行走点
    }
    
    /**
     * 打印网格（用于调试）
     */
    public void printGrid() {
        System.out.println("仓库网格 (" + width + "x" + height + "):");
        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                char c;
                switch (grid[y][x]) {
                    case WALKABLE: c = '.'; break;
                    case OBSTACLE: c = '#'; break;
                    case SHELF_FACE: c = 'S'; break;
                    case BOUNDARY: c = 'X'; break;
                    default: c = '?'; break;
                }
                System.out.print(c);
            }
            System.out.println();
        }
    }
    
    /**
     * 获取网格统计信息
     */
    public Map<String, Integer> getStatistics() {
        Map<String, Integer> stats = new HashMap<>();
        int walkable = 0, obstacle = 0, shelfFace = 0;
        
        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                switch (grid[y][x]) {
                    case WALKABLE: walkable++; break;
                    case OBSTACLE: obstacle++; break;
                    case SHELF_FACE: shelfFace++; break;
                }
            }
        }
        
        stats.put("total", width * height);
        stats.put("walkable", walkable);
        stats.put("obstacle", obstacle);
        stats.put("shelfFace", shelfFace);
        stats.put("shelfLocations", shelfLocations.size());
        
        return stats;
    }
}

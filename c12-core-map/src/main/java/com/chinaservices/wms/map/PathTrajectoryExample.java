package com.chinaservices.wms.map;

import com.chinaservices.wms.map.grid.GridPoint;
import com.chinaservices.wms.map.grid.WarehouseGrid;
import com.chinaservices.wms.map.thirdparty.JGraphTPathService;

import java.util.*;

/**
 * 路径轨迹详细演示
 * 展示如何获取和使用两个关键返回值：
 * 1. 优化后的库位访问顺序
 * 2. 完整的路径轨迹点位
 * 
 * <AUTHOR> Agent
 */
public class PathTrajectoryExample {
    
    public static void main(String[] args) {
        System.out.println("=== 路径轨迹详细演示 ===\n");
        
        // 1. 创建测试仓库
        WarehouseGrid grid = createTestWarehouse();
        printWarehouseLayout(grid);
        
        // 2. 创建路径服务
        JGraphTPathService pathService = new JGraphTPathService(grid);
        
        // 3. 演示路径轨迹功能
        demonstratePathTrajectory(pathService, grid);
        
        // 4. 演示实际应用场景
        demonstrateRealWorldUsage(pathService, grid);
        
        System.out.println("\n=== 演示完成 ===");
    }
    
    /**
     * 创建测试仓库
     */
    private static WarehouseGrid createTestWarehouse() {
        WarehouseGrid grid = new WarehouseGrid(15, 12);
        
        // 设置起始点
        grid.setStartPoint(new GridPoint(1, 10));
        
        // 添加货架A
        grid.addRectangleShelf("SHELF_A", 3, 2, 3, 4);
        grid.addShelfFace("SHELF_A", 2, 3, "LOC_A01", "A01");
        grid.addShelfFace("SHELF_A", 6, 4, "LOC_A02", "A02");
        
        // 添加货架B
        grid.addRectangleShelf("SHELF_B", 8, 2, 3, 4);
        grid.addShelfFace("SHELF_B", 7, 3, "LOC_B01", "B01");
        grid.addShelfFace("SHELF_B", 11, 4, "LOC_B02", "B02");
        
        // 添加货架C
        grid.addRectangleShelf("SHELF_C", 5, 7, 4, 2);
        grid.addShelfFace("SHELF_C", 6, 6, "LOC_C01", "C01");
        
        return grid;
    }
    
    /**
     * 打印仓库布局
     */
    private static void printWarehouseLayout(WarehouseGrid grid) {
        System.out.println("仓库布局:");
        for (int y = 0; y < grid.getHeight(); y++) {
            System.out.print("  ");
            for (int x = 0; x < grid.getWidth(); x++) {
                char c;
                GridPoint point = new GridPoint(x, y);
                
                if (point.equals(grid.getStartPoint())) {
                    c = 'S'; // Start
                } else {
                    switch (grid.getCellType(x, y)) {
                        case WALKABLE: c = '.'; break;
                        case OBSTACLE: c = '#'; break;
                        case SHELF_FACE: c = 'L'; break;
                        default: c = '?'; break;
                    }
                }
                System.out.print(c);
            }
            System.out.println();
        }
        System.out.println("图例: S=起点, .=通道, #=货架, L=库位");
    }
    
    /**
     * 演示路径轨迹功能
     */
    private static void demonstratePathTrajectory(JGraphTPathService pathService, WarehouseGrid grid) {
        System.out.println("\n=== 路径轨迹功能演示 ===");
        
        // 创建拣货任务
        List<String> locationIds = Arrays.asList("LOC_A01", "LOC_B02", "LOC_C01");
        GridPoint startPoint = grid.getStartPoint();
        
        System.out.println("拣货任务:");
        System.out.println("  起始点: " + startPoint);
        System.out.println("  目标库位: " + locationIds);
        
        // 计算路径
        JGraphTPathService.PathPlanningResult result = 
            pathService.calculateOptimalPath(locationIds, startPoint);
        
        if (result.isSuccess()) {
            System.out.println("\n📍 返回值1: 优化后的库位访问顺序");
            List<GridPoint> locationOrder = result.getOptimizedLocationOrder();
            for (int i = 0; i < locationOrder.size(); i++) {
                GridPoint point = locationOrder.get(i);
                WarehouseGrid.ShelfLocation location = grid.getShelfLocation(point);
                String locationCode = location != null ? location.getLocationCode() : "Unknown";
                System.out.println("  " + (i + 1) + ". " + locationCode + " at " + point);
            }
            
            System.out.println("\n🛤️  返回值2: 完整的路径轨迹点位");
            List<GridPoint> trajectory = result.getPathTrajectory();
            System.out.println("  轨迹总长度: " + trajectory.size() + " 步");
            System.out.println("  详细轨迹:");
            
            for (int i = 0; i < trajectory.size(); i++) {
                GridPoint point = trajectory.get(i);
                String description = getPointDescription(point, grid, locationOrder);
                System.out.println("    步骤 " + (i + 1) + ": " + point + " " + description);
            }
            
            System.out.println("\n📊 路径统计:");
            System.out.println("  总距离: " + String.format("%.2f", result.getTotalDistance()));
            System.out.println("  计算耗时: " + result.getComputationTime() + "ms");
            System.out.println("  算法: " + result.getAlgorithm());
        }
    }
    
    /**
     * 获取点位描述
     */
    private static String getPointDescription(GridPoint point, WarehouseGrid grid, List<GridPoint> locationOrder) {
        if (point.equals(grid.getStartPoint())) {
            return "(起始点)";
        }
        
        WarehouseGrid.ShelfLocation location = grid.getShelfLocation(point);
        if (location != null) {
            int orderIndex = locationOrder.indexOf(point);
            if (orderIndex >= 0) {
                return "(到达库位 " + location.getLocationCode() + " - 第" + (orderIndex + 1) + "个目标)";
            } else {
                return "(库位 " + location.getLocationCode() + ")";
            }
        }
        
        switch (grid.getCellType(point)) {
            case WALKABLE: return "(通道)";
            case OBSTACLE: return "(货架)";
            default: return "";
        }
    }
    
    /**
     * 演示实际应用场景
     */
    private static void demonstrateRealWorldUsage(JGraphTPathService pathService, WarehouseGrid grid) {
        System.out.println("\n=== 实际应用场景演示 ===");
        
        List<String> locationIds = Arrays.asList("LOC_A02", "LOC_B01", "LOC_C01", "LOC_A01");
        GridPoint startPoint = grid.getStartPoint();
        
        JGraphTPathService.PathPlanningResult result = 
            pathService.calculateOptimalPath(locationIds, startPoint);
        
        if (result.isSuccess()) {
            System.out.println("🎯 应用场景1: WMS系统集成");
            System.out.println("  // 获取优化后的库位访问顺序");
            System.out.println("  List<GridPoint> locationOrder = result.getOptimizedLocationOrder();");
            System.out.println("  实际顺序: " + formatLocationOrder(result.getOptimizedLocationOrder(), grid));
            
            System.out.println("\n🤖 应用场景2: AGV/机器人导航");
            System.out.println("  // 获取详细的移动轨迹");
            System.out.println("  List<GridPoint> trajectory = result.getPathTrajectory();");
            System.out.println("  轨迹点数: " + result.getPathTrajectory().size());
            System.out.println("  导航指令: " + generateNavigationCommands(result.getPathTrajectory()));
            
            System.out.println("\n📱 应用场景3: 移动端显示");
            System.out.println("  总距离: " + String.format("%.1f米", result.getTotalDistance()));
            System.out.println("  预计时间: " + String.format("%.1f秒", result.getTotalDistance() / 1.5));
            System.out.println("  库位数量: " + result.getOptimizedLocationOrder().size());
            
            System.out.println("\n🔧 应用场景4: 系统监控");
            System.out.println("  算法类型: " + result.getAlgorithm());
            System.out.println("  计算性能: " + result.getComputationTime() + "ms");
            System.out.println("  路径效率: " + calculatePathEfficiency(result));
        }
    }
    
    /**
     * 格式化库位访问顺序
     */
    private static String formatLocationOrder(List<GridPoint> locationOrder, WarehouseGrid grid) {
        List<String> codes = new ArrayList<>();
        for (GridPoint point : locationOrder) {
            WarehouseGrid.ShelfLocation location = grid.getShelfLocation(point);
            if (location != null) {
                codes.add(location.getLocationCode());
            }
        }
        return String.join(" → ", codes);
    }
    
    /**
     * 生成导航指令
     */
    private static String generateNavigationCommands(List<GridPoint> trajectory) {
        if (trajectory.size() < 2) return "无移动";
        
        List<String> commands = new ArrayList<>();
        for (int i = 1; i < Math.min(4, trajectory.size()); i++) {
            GridPoint from = trajectory.get(i - 1);
            GridPoint to = trajectory.get(i);
            
            int dx = to.getX() - from.getX();
            int dy = to.getY() - from.getY();
            
            if (dx > 0) commands.add("右");
            else if (dx < 0) commands.add("左");
            else if (dy > 0) commands.add("下");
            else if (dy < 0) commands.add("上");
        }
        
        if (trajectory.size() > 4) {
            commands.add("...");
        }
        
        return String.join("→", commands);
    }
    
    /**
     * 计算路径效率
     */
    private static String calculatePathEfficiency(JGraphTPathService.PathPlanningResult result) {
        double actualDistance = result.getTotalDistance();
        double directDistance = result.getOptimizedLocationOrder().size() * 5.0; // 假设直线距离
        double efficiency = (directDistance / actualDistance) * 100;
        return String.format("%.1f%%", efficiency);
    }
}

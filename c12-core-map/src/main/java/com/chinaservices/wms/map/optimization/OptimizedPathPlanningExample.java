package com.chinaservices.wms.map.optimization;

import com.chinaservices.wms.map.optimization.OptimizedPathPlanningService.*;

import java.util.*;

/**
 * 优化路径规划服务使用示例
 * 演示两个核心功能的使用方法
 * 
 * <AUTHOR> Agent
 */
public class OptimizedPathPlanningExample {
    
    public static void main(String[] args) {
        System.out.println("=== 优化路径规划服务演示 ===\n");
        
        OptimizedPathPlanningService service = new OptimizedPathPlanningService();
        
        // 演示方法1：路径规划优化
        demonstratePathPlanning(service);
        
        System.out.println("\n" + "=".repeat(50) + "\n");
        
        // 演示方法2：货架切割和库位生成
        demonstrateShelfGeneration(service);
        
        System.out.println("\n=== 演示完成 ===");
    }
    
    /**
     * 演示方法1：路径规划优化
     */
    private static void demonstratePathPlanning(OptimizedPathPlanningService service) {
        System.out.println("【方法1：路径规划优化】");
        
        // 1. 创建库位数据（code + 4个角点坐标）
        List<StorageLocation> storageLocations = createTestStorageLocations();
        
        // 2. 创建线路图（多个线路段）
        List<RouteSegment> routeSegments = createTestRouteSegments();
        
        // 3. 设置起始点
        Point startPoint = new Point(0, 0);
        
        System.out.println("输入数据:");
        System.out.println("  库位数量: " + storageLocations.size());
        System.out.println("  线路段数量: " + routeSegments.size());
        System.out.println("  起始点: " + startPoint);
        
        // 4. 执行路径规划
        PathPlanningResult result = service.optimizePathPlanning(storageLocations, routeSegments, startPoint);
        
        // 5. 输出结果
        System.out.println("\n路径规划结果:");
        System.out.println("  " + result);
        System.out.println("  优化后的库位访问顺序: " + result.getOptimizedLocationCodes());
        System.out.println("  路径坐标点数量: " + result.getPathCoordinates().size());
        System.out.println("  前10个路径坐标: " + 
                         result.getPathCoordinates().subList(0, Math.min(10, result.getPathCoordinates().size())));
    }
    
    /**
     * 演示方法2：货架切割和库位生成
     */
    private static void demonstrateShelfGeneration(OptimizedPathPlanningService service) {
        System.out.println("【方法2：货架切割和库位生成】");
        
        // 1. 设置货架参数
        double length = 10.0;  // 长度
        double width = 5.0;    // 宽度
        double height = 3.0;   // 高度
        
        // 2. 设置货架底部4个角点（矩形）
        List<Point> bottomCorners = Arrays.asList(
            new Point(0, 0),    // 左下角
            new Point(10, 0),   // 右下角
            new Point(10, 5),   // 右上角
            new Point(0, 5)     // 左上角
        );
        
        // 3. 设置切割参数
        int columns = 5;  // 5列
        int rows = 2;     // 2行
        String codePattern = "A{row:02d}{col:02d}";  // 编号规则
        
        System.out.println("输入参数:");
        System.out.println("  货架尺寸: " + length + " x " + width + " x " + height);
        System.out.println("  底部角点: " + bottomCorners);
        System.out.println("  切割规格: " + columns + "列 x " + rows + "行");
        System.out.println("  编号规则: " + codePattern);
        
        // 4. 生成库位
        List<ShelfLocationResult> locations = service.generateShelfLocations(
            length, width, height, bottomCorners, columns, rows, codePattern);
        
        // 5. 输出结果
        System.out.println("\n库位生成结果:");
        System.out.println("  生成库位数量: " + locations.size());
        System.out.println("  库位详情:");
        
        for (int i = 0; i < Math.min(10, locations.size()); i++) {
            ShelfLocationResult location = locations.get(i);
            System.out.println("    " + location);
        }
        
        if (locations.size() > 10) {
            System.out.println("    ... 还有 " + (locations.size() - 10) + " 个库位");
        }
    }
    
    /**
     * 创建测试库位数据
     */
    private static List<StorageLocation> createTestStorageLocations() {
        List<StorageLocation> locations = new ArrayList<>();
        
        // 库位A01 - 正方形库位
        locations.add(new StorageLocation("A01", Arrays.asList(
            new Point(2, 2), new Point(3, 2), new Point(3, 3), new Point(2, 3)
        )));
        
        // 库位B02 - 矩形库位
        locations.add(new StorageLocation("B02", Arrays.asList(
            new Point(5, 1), new Point(7, 1), new Point(7, 2), new Point(5, 2)
        )));
        
        // 库位C03 - 另一个正方形库位
        locations.add(new StorageLocation("C03", Arrays.asList(
            new Point(8, 4), new Point(9, 4), new Point(9, 5), new Point(8, 5)
        )));
        
        // 库位D04 - 重合点测试（与其他库位有重合的角点）
        locations.add(new StorageLocation("D04", Arrays.asList(
            new Point(3, 3), new Point(4, 3), new Point(4, 4), new Point(3, 4)
        )));
        
        return locations;
    }
    
    /**
     * 创建测试线路段
     */
    private static List<RouteSegment> createTestRouteSegments() {
        List<RouteSegment> segments = new ArrayList<>();
        
        // 线路段1：主通道（水平）
        segments.add(new RouteSegment(Arrays.asList(
            new Point(0, 0), new Point(2, 0), new Point(4, 0), 
            new Point(6, 0), new Point(8, 0), new Point(10, 0)
        )));
        
        // 线路段2：垂直连接通道
        segments.add(new RouteSegment(Arrays.asList(
            new Point(10, 0), new Point(10, 2), new Point(10, 4), new Point(10, 6)
        )));
        
        // 线路段3：上层水平通道
        segments.add(new RouteSegment(Arrays.asList(
            new Point(10, 6), new Point(8, 6), new Point(6, 6), 
            new Point(4, 6), new Point(2, 6), new Point(0, 6)
        )));
        
        // 线路段4：左侧垂直通道
        segments.add(new RouteSegment(Arrays.asList(
            new Point(0, 6), new Point(0, 4), new Point(0, 2), new Point(0, 0)
        )));
        
        // 线路段5：中间交叉通道
        segments.add(new RouteSegment(Arrays.asList(
            new Point(5, 0), new Point(5, 2), new Point(5, 4), new Point(5, 6)
        )));
        
        return segments;
    }
    
    /**
     * 辅助方法：打印分隔线
     */
    private static void printSeparator(String title) {
        System.out.println("\n" + "=".repeat(20) + " " + title + " " + "=".repeat(20));
    }
    
    /**
     * 辅助方法：格式化输出点位列表
     */
    private static String formatPointList(List<Point> points) {
        return points.stream()
                    .map(Point::toString)
                    .reduce((a, b) -> a + ", " + b)
                    .orElse("");
    }
}

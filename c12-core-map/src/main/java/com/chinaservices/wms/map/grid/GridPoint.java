package com.chinaservices.wms.map.grid;

/**
 * 网格点 - 表示仓库中的一个网格坐标
 * 
 * <AUTHOR> Agent
 */
public class GridPoint {
    
    /**
     * X坐标（列）
     */
    private int x;
    
    /**
     * Y坐标（行）
     */
    private int y;
    
    /**
     * Z坐标（层）- 用于多层货架
     */
    private int z;
    
    public GridPoint() {}
    
    public GridPoint(int x, int y) {
        this.x = x;
        this.y = y;
        this.z = 0;
    }
    
    public GridPoint(int x, int y, int z) {
        this.x = x;
        this.y = y;
        this.z = z;
    }
    
    public int getX() { return x; }
    public void setX(int x) { this.x = x; }
    
    public int getY() { return y; }
    public void setY(int y) { this.y = y; }
    
    public int getZ() { return z; }
    public void setZ(int z) { this.z = z; }
    
    /**
     * 计算到另一个点的曼哈顿距离
     */
    public int manhattanDistance(GridPoint other) {
        return Math.abs(this.x - other.x) + Math.abs(this.y - other.y) + Math.abs(this.z - other.z);
    }
    
    /**
     * 计算到另一个点的欧几里得距离
     */
    public double euclideanDistance(GridPoint other) {
        int dx = this.x - other.x;
        int dy = this.y - other.y;
        int dz = this.z - other.z;
        return Math.sqrt(dx * dx + dy * dy + dz * dz);
    }
    
    /**
     * 获取相邻的4个点（上下左右）
     */
    public GridPoint[] getNeighbors() {
        return new GridPoint[] {
            new GridPoint(x, y - 1, z), // 上
            new GridPoint(x, y + 1, z), // 下
            new GridPoint(x - 1, y, z), // 左
            new GridPoint(x + 1, y, z)  // 右
        };
    }
    
    /**
     * 获取相邻的8个点（包括对角线）
     */
    public GridPoint[] getNeighbors8() {
        return new GridPoint[] {
            new GridPoint(x, y - 1, z),     // 上
            new GridPoint(x, y + 1, z),     // 下
            new GridPoint(x - 1, y, z),     // 左
            new GridPoint(x + 1, y, z),     // 右
            new GridPoint(x - 1, y - 1, z), // 左上
            new GridPoint(x + 1, y - 1, z), // 右上
            new GridPoint(x - 1, y + 1, z), // 左下
            new GridPoint(x + 1, y + 1, z)  // 右下
        };
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        GridPoint gridPoint = (GridPoint) obj;
        return x == gridPoint.x && y == gridPoint.y && z == gridPoint.z;
    }
    
    @Override
    public int hashCode() {
        return x * 31 * 31 + y * 31 + z;
    }
    
    @Override
    public String toString() {
        return String.format("(%d,%d,%d)", x, y, z);
    }
    
    /**
     * 创建点的副本
     */
    public GridPoint copy() {
        return new GridPoint(x, y, z);
    }
    
    /**
     * 检查是否在指定范围内
     */
    public boolean isInBounds(int minX, int minY, int maxX, int maxY) {
        return x >= minX && x <= maxX && y >= minY && y <= maxY;
    }
}

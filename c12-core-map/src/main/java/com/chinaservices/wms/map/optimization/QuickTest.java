package com.chinaservices.wms.map.optimization;

import com.chinaservices.wms.map.optimization.OptimizedPathPlanningService.*;

import java.util.*;

/**
 * 快速测试类
 * 验证两个核心方法的功能
 * 
 * <AUTHOR> Agent
 */
public class QuickTest {
    
    public static void main(String[] args) {
        System.out.println("=== 快速功能测试 ===\n");
        
        // 测试方法1：路径规划
        testPathPlanning();
        
        System.out.println("\n" + "=".repeat(60) + "\n");
        
        // 测试方法2：货架生成
        testShelfGeneration();
        
        // 测试工具类
        testUtils();
        
        System.out.println("\n=== 测试完成 ===");
    }
    
    /**
     * 测试路径规划功能
     */
    private static void testPathPlanning() {
        System.out.println("【测试方法1：路径规划优化】");
        
        try {
            // 准备测试数据
            Map<String, List<String>> locationData = new HashMap<>();
            locationData.put("A01", Arrays.asList("2,2", "3,2", "3,3", "2,3"));
            locationData.put("B02", Arrays.asList("5,1", "7,1", "7,2", "5,2"));
            locationData.put("C03", Arrays.asList("8,4", "9,4", "9,5", "8,5"));
            
            List<List<String>> routeData = Arrays.asList(
                Arrays.asList("0,0", "2,0", "4,0", "6,0", "8,0", "10,0"),
                Arrays.asList("10,0", "10,2", "10,4", "10,6"),
                Arrays.asList("10,6", "8,6", "6,6", "4,6", "2,6", "0,6")
            );
            
            String startCoordinate = "0,0";
            
            // 执行路径规划
            PathPlanningResult result = PathPlanningUtils.planPath(locationData, routeData, startCoordinate);
            
            // 输出结果
            System.out.println(PathPlanningUtils.formatPathPlanningResult(result));
            
        } catch (Exception e) {
            System.err.println("路径规划测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试货架生成功能
     */
    private static void testShelfGeneration() {
        System.out.println("【测试方法2：货架切割和库位生成】");
        
        try {
            // 准备测试数据
            double length = 10.0;
            double width = 5.0;
            double height = 3.0;
            List<String> bottomCorners = Arrays.asList("0,0", "10,0", "10,5", "0,5");
            int columns = 5;
            int rows = 2;
            String codePattern = "A{row:02d}{col:02d}";
            
            // 执行货架生成
            List<ShelfLocationResult> results = PathPlanningUtils.generateShelf(
                length, width, height, bottomCorners, columns, rows, codePattern);
            
            // 输出结果
            System.out.println(PathPlanningUtils.formatShelfLocationResults(results));
            
        } catch (Exception e) {
            System.err.println("货架生成测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试工具类功能
     */
    private static void testUtils() {
        System.out.println("\n【测试工具类功能】");
        
        // 测试数据验证
        Map<String, List<String>> validLocationData = new HashMap<>();
        validLocationData.put("A01", Arrays.asList("1,1", "2,1", "2,2", "1,2"));
        
        Map<String, List<String>> invalidLocationData = new HashMap<>();
        invalidLocationData.put("B01", Arrays.asList("1,1", "2,1")); // 只有2个点，应该有4个
        
        List<List<String>> validRouteData = Arrays.asList(
            Arrays.asList("0,0", "1,0", "2,0"),
            Arrays.asList("2,0", "2,1", "2,2")
        );
        
        List<List<String>> invalidRouteData = Arrays.asList(
            Arrays.asList("0,0") // 只有1个点，至少需要2个
        );
        
        System.out.println("有效库位数据验证: " + PathPlanningUtils.validateLocationData(validLocationData));
        System.out.println("无效库位数据验证: " + PathPlanningUtils.validateLocationData(invalidLocationData));
        System.out.println("有效线路数据验证: " + PathPlanningUtils.validateRouteData(validRouteData));
        System.out.println("无效线路数据验证: " + PathPlanningUtils.validateRouteData(invalidRouteData));
    }
    
    /**
     * 性能测试
     */
    private static void performanceTest() {
        System.out.println("\n【性能测试】");
        
        // 生成大量测试数据
        Map<String, List<String>> locationData = generateLargeLocationData(100);
        List<List<String>> routeData = generateLargeRouteData(20, 50);
        String startCoordinate = "0,0";
        
        long startTime = System.currentTimeMillis();
        PathPlanningResult result = PathPlanningUtils.planPath(locationData, routeData, startCoordinate);
        long endTime = System.currentTimeMillis();
        
        System.out.println("大规模测试结果:");
        System.out.println("  库位数量: " + locationData.size());
        System.out.println("  线路段数量: " + routeData.size());
        System.out.println("  总计算时间: " + (endTime - startTime) + "ms");
        System.out.println("  算法计算时间: " + result.getComputationTime() + "ms");
        System.out.println("  路径总距离: " + String.format("%.2f", result.getTotalDistance()));
    }
    
    /**
     * 生成大量库位测试数据
     */
    private static Map<String, List<String>> generateLargeLocationData(int count) {
        Map<String, List<String>> locationData = new HashMap<>();
        Random random = new Random();
        
        for (int i = 0; i < count; i++) {
            String code = String.format("LOC_%03d", i + 1);
            
            // 随机生成一个小矩形库位
            double x = random.nextDouble() * 100;
            double y = random.nextDouble() * 100;
            double w = 1 + random.nextDouble() * 2; // 宽度1-3
            double h = 1 + random.nextDouble() * 2; // 高度1-3
            
            List<String> corners = Arrays.asList(
                String.format("%.2f,%.2f", x, y),
                String.format("%.2f,%.2f", x + w, y),
                String.format("%.2f,%.2f", x + w, y + h),
                String.format("%.2f,%.2f", x, y + h)
            );
            
            locationData.put(code, corners);
        }
        
        return locationData;
    }
    
    /**
     * 生成大量线路测试数据
     */
    private static List<List<String>> generateLargeRouteData(int segmentCount, int pointsPerSegment) {
        List<List<String>> routeData = new ArrayList<>();
        Random random = new Random();
        
        for (int i = 0; i < segmentCount; i++) {
            List<String> segment = new ArrayList<>();
            
            // 生成一条线路段
            double startX = random.nextDouble() * 100;
            double startY = random.nextDouble() * 100;
            
            for (int j = 0; j < pointsPerSegment; j++) {
                double x = startX + j * (1 + random.nextDouble());
                double y = startY + (random.nextDouble() - 0.5) * 2; // 轻微波动
                segment.add(String.format("%.2f,%.2f", x, y));
            }
            
            routeData.add(segment);
        }
        
        return routeData;
    }
}

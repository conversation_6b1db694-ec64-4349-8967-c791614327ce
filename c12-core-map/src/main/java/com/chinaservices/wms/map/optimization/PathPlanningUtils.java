package com.chinaservices.wms.map.optimization;

import com.chinaservices.wms.map.optimization.OptimizedPathPlanningService.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 路径规划工具类
 * 提供便捷的API来简化路径规划服务的使用
 * 
 * <AUTHOR> Agent
 */
public class PathPlanningUtils {
    
    /**
     * 从字符串坐标创建库位
     * 
     * @param locationData Map<String, List<String>> - key为库位code，value为4个角点坐标字符串
     * @return 库位列表
     */
    public static List<StorageLocation> createStorageLocationsFromStrings(Map<String, List<String>> locationData) {
        List<StorageLocation> locations = new ArrayList<>();
        
        for (Map.Entry<String, List<String>> entry : locationData.entrySet()) {
            String code = entry.getKey();
            List<String> coordinateStrings = entry.getValue();
            
            if (coordinateStrings.size() != 4) {
                throw new IllegalArgumentException("库位 " + code + " 必须有4个角点坐标");
            }
            
            List<Point> corners = coordinateStrings.stream()
                                                 .map(Point::new)
                                                 .collect(Collectors.toList());
            
            locations.add(new StorageLocation(code, corners));
        }
        
        return locations;
    }
    
    /**
     * 从字符串坐标创建线路段
     * 
     * @param routeData List<List<String>> - 每个内层List代表一个线路段的点位坐标字符串
     * @return 线路段列表
     */
    public static List<RouteSegment> createRouteSegmentsFromStrings(List<List<String>> routeData) {
        List<RouteSegment> segments = new ArrayList<>();
        
        for (List<String> segmentData : routeData) {
            List<Point> points = segmentData.stream()
                                           .map(Point::new)
                                           .collect(Collectors.toList());
            segments.add(new RouteSegment(points));
        }
        
        return segments;
    }
    
    /**
     * 简化的路径规划方法
     * 
     * @param locationData 库位数据 Map<code, List<坐标字符串>>
     * @param routeData 线路数据 List<List<坐标字符串>>
     * @param startCoordinate 起始点坐标字符串
     * @return 路径规划结果
     */
    public static PathPlanningResult planPath(Map<String, List<String>> locationData,
                                            List<List<String>> routeData,
                                            String startCoordinate) {
        
        OptimizedPathPlanningService service = new OptimizedPathPlanningService();
        
        // 转换数据格式
        List<StorageLocation> locations = createStorageLocationsFromStrings(locationData);
        List<RouteSegment> routes = createRouteSegmentsFromStrings(routeData);
        Point startPoint = new Point(startCoordinate);
        
        // 执行路径规划
        return service.optimizePathPlanning(locations, routes, startPoint);
    }
    
    /**
     * 简化的货架生成方法
     * 
     * @param length 货架长度
     * @param width 货架宽度
     * @param height 货架高度
     * @param bottomCornerCoordinates 底部4个角点坐标字符串
     * @param columns 列数
     * @param rows 行数
     * @param codePattern 编号规则
     * @return 库位生成结果
     */
    public static List<ShelfLocationResult> generateShelf(double length, double width, double height,
                                                         List<String> bottomCornerCoordinates,
                                                         int columns, int rows,
                                                         String codePattern) {
        
        OptimizedPathPlanningService service = new OptimizedPathPlanningService();
        
        // 转换坐标格式
        List<Point> bottomCorners = bottomCornerCoordinates.stream()
                                                          .map(Point::new)
                                                          .collect(Collectors.toList());
        
        // 执行货架生成
        return service.generateShelfLocations(length, width, height, bottomCorners, columns, rows, codePattern);
    }
    
    /**
     * 批量生成多个货架的库位
     * 
     * @param shelfConfigs 货架配置列表
     * @return 所有库位结果
     */
    public static List<ShelfLocationResult> generateMultipleShelves(List<ShelfConfig> shelfConfigs) {
        List<ShelfLocationResult> allLocations = new ArrayList<>();
        
        for (ShelfConfig config : shelfConfigs) {
            List<ShelfLocationResult> shelfLocations = generateShelf(
                config.length, config.width, config.height,
                config.bottomCornerCoordinates, config.columns, config.rows,
                config.codePattern
            );
            allLocations.addAll(shelfLocations);
        }
        
        return allLocations;
    }
    
    /**
     * 货架配置类
     */
    public static class ShelfConfig {
        public double length;
        public double width;
        public double height;
        public List<String> bottomCornerCoordinates;
        public int columns;
        public int rows;
        public String codePattern;
        
        public ShelfConfig(double length, double width, double height,
                          List<String> bottomCornerCoordinates,
                          int columns, int rows, String codePattern) {
            this.length = length;
            this.width = width;
            this.height = height;
            this.bottomCornerCoordinates = bottomCornerCoordinates;
            this.columns = columns;
            this.rows = rows;
            this.codePattern = codePattern;
        }
    }
    
    /**
     * 验证库位数据格式
     */
    public static boolean validateLocationData(Map<String, List<String>> locationData) {
        for (Map.Entry<String, List<String>> entry : locationData.entrySet()) {
            String code = entry.getKey();
            List<String> coordinates = entry.getValue();
            
            // 检查库位code
            if (code == null || code.trim().isEmpty()) {
                System.err.println("库位code不能为空");
                return false;
            }
            
            // 检查坐标数量
            if (coordinates == null || coordinates.size() != 4) {
                System.err.println("库位 " + code + " 必须有4个角点坐标");
                return false;
            }
            
            // 检查坐标格式
            for (String coord : coordinates) {
                if (!isValidCoordinate(coord)) {
                    System.err.println("库位 " + code + " 的坐标格式无效: " + coord);
                    return false;
                }
            }
        }
        
        return true;
    }
    
    /**
     * 验证线路数据格式
     */
    public static boolean validateRouteData(List<List<String>> routeData) {
        if (routeData == null || routeData.isEmpty()) {
            System.err.println("线路数据不能为空");
            return false;
        }
        
        for (int i = 0; i < routeData.size(); i++) {
            List<String> segment = routeData.get(i);
            
            if (segment == null || segment.size() < 2) {
                System.err.println("线路段 " + i + " 至少需要2个点位");
                return false;
            }
            
            for (String coord : segment) {
                if (!isValidCoordinate(coord)) {
                    System.err.println("线路段 " + i + " 的坐标格式无效: " + coord);
                    return false;
                }
            }
        }
        
        return true;
    }
    
    /**
     * 检查坐标字符串格式是否有效
     */
    private static boolean isValidCoordinate(String coordinate) {
        if (coordinate == null || coordinate.trim().isEmpty()) {
            return false;
        }
        
        try {
            String[] parts = coordinate.split(",");
            if (parts.length != 2) {
                return false;
            }
            
            Double.parseDouble(parts[0].trim());
            Double.parseDouble(parts[1].trim());
            return true;
            
        } catch (NumberFormatException e) {
            return false;
        }
    }
    
    /**
     * 格式化路径规划结果为易读的字符串
     */
    public static String formatPathPlanningResult(PathPlanningResult result) {
        StringBuilder sb = new StringBuilder();
        sb.append("路径规划结果:\n");
        sb.append("  库位访问顺序: ").append(result.getOptimizedLocationCodes()).append("\n");
        sb.append("  路径点位数量: ").append(result.getPathCoordinates().size()).append("\n");
        sb.append("  总距离: ").append(String.format("%.2f", result.getTotalDistance())).append("\n");
        sb.append("  计算耗时: ").append(result.getComputationTime()).append("ms\n");
        
        if (!result.getPathCoordinates().isEmpty()) {
            sb.append("  路径坐标 (前10个): ");
            List<String> firstTen = result.getPathCoordinates().subList(0, 
                Math.min(10, result.getPathCoordinates().size()));
            sb.append(String.join(" -> ", firstTen));
            if (result.getPathCoordinates().size() > 10) {
                sb.append(" ...");
            }
        }
        
        return sb.toString();
    }
    
    /**
     * 格式化货架生成结果为易读的字符串
     */
    public static String formatShelfLocationResults(List<ShelfLocationResult> results) {
        StringBuilder sb = new StringBuilder();
        sb.append("货架库位生成结果:\n");
        sb.append("  生成库位数量: ").append(results.size()).append("\n");
        
        for (int i = 0; i < Math.min(5, results.size()); i++) {
            ShelfLocationResult result = results.get(i);
            sb.append("  ").append(result.getLocationCode()).append(": ");
            sb.append(result.getCorners().stream()
                           .map(Point::toString)
                           .collect(Collectors.joining(", ")));
            sb.append("\n");
        }
        
        if (results.size() > 5) {
            sb.append("  ... 还有 ").append(results.size() - 5).append(" 个库位");
        }
        
        return sb.toString();
    }
}

package com.chinaservices.wms.map.optimization;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 优化的路径规划服务
 * 提供两个核心功能：
 * 1. 基于线路图的最短路径规划和库位排序
 * 2. 货架切割和库位生成
 * 
 * <AUTHOR> Agent
 */
public class OptimizedPathPlanningService {
    
    /**
     * 点位坐标类
     */
    public static class Point {
        private double x;
        private double y;
        
        public Point(double x, double y) {
            this.x = x;
            this.y = y;
        }
        
        public Point(String coordinateStr) {
            String[] coords = coordinateStr.split(",");
            this.x = Double.parseDouble(coords[0].trim());
            this.y = Double.parseDouble(coords[1].trim());
        }
        
        public double getX() { return x; }
        public double getY() { return y; }
        
        public double distanceTo(Point other) {
            double dx = this.x - other.x;
            double dy = this.y - other.y;
            return Math.sqrt(dx * dx + dy * dy);
        }
        
        @Override
        public boolean equals(Object obj) {
            if (this == obj) return true;
            if (obj == null || getClass() != obj.getClass()) return false;
            Point point = (Point) obj;
            return Double.compare(point.x, x) == 0 && Double.compare(point.y, y) == 0;
        }
        
        @Override
        public int hashCode() {
            return Objects.hash(x, y);
        }
        
        @Override
        public String toString() {
            return String.format("%.2f,%.2f", x, y);
        }
    }
    
    /**
     * 库位信息类
     */
    public static class StorageLocation {
        private String code;
        private List<Point> corners; // 4个角点坐标
        private Point anchorPoint;   // 在线路上的锚点
        
        public StorageLocation(String code, List<Point> corners) {
            this.code = code;
            this.corners = corners;
        }
        
        public String getCode() { return code; }
        public List<Point> getCorners() { return corners; }
        public Point getAnchorPoint() { return anchorPoint; }
        public void setAnchorPoint(Point anchorPoint) { this.anchorPoint = anchorPoint; }
        
        /**
         * 获取库位中心点
         */
        public Point getCenterPoint() {
            double avgX = corners.stream().mapToDouble(Point::getX).average().orElse(0);
            double avgY = corners.stream().mapToDouble(Point::getY).average().orElse(0);
            return new Point(avgX, avgY);
        }
    }
    
    /**
     * 线路段类
     */
    public static class RouteSegment {
        private List<Point> points;
        
        public RouteSegment(List<Point> points) {
            this.points = points;
        }
        
        public List<Point> getPoints() { return points; }
        
        /**
         * 找到距离指定点最近的线路点
         */
        public Point findNearestPoint(Point target) {
            Point nearest = null;
            double minDistance = Double.MAX_VALUE;
            
            for (Point point : points) {
                double distance = point.distanceTo(target);
                if (distance < minDistance) {
                    minDistance = distance;
                    nearest = point;
                }
            }
            
            return nearest;
        }
        
        /**
         * 获取线路段总长度
         */
        public double getTotalLength() {
            double length = 0;
            for (int i = 0; i < points.size() - 1; i++) {
                length += points.get(i).distanceTo(points.get(i + 1));
            }
            return length;
        }
    }
    
    /**
     * 路径规划结果类
     */
    public static class PathPlanningResult {
        private List<String> optimizedLocationCodes; // 优化后的库位code排序
        private List<String> pathCoordinates;        // 路径坐标字符串数组
        private double totalDistance;
        private long computationTime;
        
        public PathPlanningResult() {
            this.optimizedLocationCodes = new ArrayList<>();
            this.pathCoordinates = new ArrayList<>();
        }
        
        // Getters and setters
        public List<String> getOptimizedLocationCodes() { return optimizedLocationCodes; }
        public void setOptimizedLocationCodes(List<String> optimizedLocationCodes) {
            this.optimizedLocationCodes = optimizedLocationCodes;
        }
        
        public List<String> getPathCoordinates() { return pathCoordinates; }
        public void setPathCoordinates(List<String> pathCoordinates) {
            this.pathCoordinates = pathCoordinates;
        }
        
        public double getTotalDistance() { return totalDistance; }
        public void setTotalDistance(double totalDistance) { this.totalDistance = totalDistance; }
        
        public long getComputationTime() { return computationTime; }
        public void setComputationTime(long computationTime) { this.computationTime = computationTime; }
        
        @Override
        public String toString() {
            return String.format("PathPlanningResult[locations=%d, pathPoints=%d, distance=%.2f, time=%dms]",
                               optimizedLocationCodes.size(), pathCoordinates.size(), totalDistance, computationTime);
        }
    }
    
    /**
     * 货架库位生成结果类
     */
    public static class ShelfLocationResult {
        private String locationCode;
        private List<Point> corners; // 4个角点坐标
        
        public ShelfLocationResult(String locationCode, List<Point> corners) {
            this.locationCode = locationCode;
            this.corners = corners;
        }
        
        public String getLocationCode() { return locationCode; }
        public List<Point> getCorners() { return corners; }
        
        @Override
        public String toString() {
            return String.format("%s: [%s]", locationCode, 
                               corners.stream().map(Point::toString).collect(Collectors.joining(", ")));
        }
    }
    
    /**
     * 方法1: 路径规划优化
     * 
     * @param storageLocations 库位列表（code + 4个角点坐标）
     * @param routeSegments 线路图（多个线路段，每个段包含多个点位）
     * @param startPoint 起始点
     * @return 路径规划结果（库位code排序 + 路径坐标）
     */
    public PathPlanningResult optimizePathPlanning(
            List<StorageLocation> storageLocations,
            List<RouteSegment> routeSegments,
            Point startPoint) {
        
        long startTime = System.currentTimeMillis();
        PathPlanningResult result = new PathPlanningResult();
        
        try {
            // 1. 为每个库位找到在线路上的锚点
            findAnchorPointsForLocations(storageLocations, routeSegments);
            
            // 2. 构建完整的线路图
            List<Point> completeRoute = buildCompleteRoute(routeSegments);
            
            // 3. 使用TSP算法优化库位访问顺序
            List<String> optimizedOrder = optimizeLocationOrder(storageLocations, startPoint);
            result.setOptimizedLocationCodes(optimizedOrder);
            
            // 4. 构建完整的路径坐标
            List<String> pathCoordinates = buildPathCoordinates(storageLocations, optimizedOrder, 
                                                              completeRoute, startPoint);
            result.setPathCoordinates(pathCoordinates);
            
            // 5. 计算总距离
            double totalDistance = calculateTotalDistance(pathCoordinates);
            result.setTotalDistance(totalDistance);
            
        } catch (Exception e) {
            System.err.println("路径规划失败: " + e.getMessage());
            e.printStackTrace();
        } finally {
            result.setComputationTime(System.currentTimeMillis() - startTime);
        }
        
        return result;
    }
    
    /**
     * 方法2: 货架切割和库位生成
     * 
     * @param length 货架长度
     * @param width 货架宽度
     * @param height 货架高度
     * @param bottomCorners 货架底部4个角点坐标
     * @param columns 列数
     * @param rows 行数
     * @param codePattern 编号规则（如 "A{row:02d}{col:02d}"）
     * @return 库位列表（编号 + 4个角点坐标）
     */
    public List<ShelfLocationResult> generateShelfLocations(
            double length, double width, double height,
            List<Point> bottomCorners,
            int columns, int rows,
            String codePattern) {
        
        List<ShelfLocationResult> locations = new ArrayList<>();
        
        try {
            // 1. 验证输入参数
            if (bottomCorners.size() != 4) {
                throw new IllegalArgumentException("货架底部必须有4个角点");
            }
            
            // 2. 计算每个库位的尺寸
            double cellWidth = length / columns;
            double cellHeight = width / rows;
            
            // 3. 确定货架的方向向量
            Point corner1 = bottomCorners.get(0);
            Point corner2 = bottomCorners.get(1);
            Point corner4 = bottomCorners.get(3);
            
            // 计算长度方向和宽度方向的单位向量
            double lengthVectorX = (corner2.getX() - corner1.getX()) / columns;
            double lengthVectorY = (corner2.getY() - corner1.getY()) / columns;
            double widthVectorX = (corner4.getX() - corner1.getX()) / rows;
            double widthVectorY = (corner4.getY() - corner1.getY()) / rows;
            
            // 4. 生成每个库位
            for (int row = 0; row < rows; row++) {
                for (int col = 0; col < columns; col++) {
                    // 生成库位编号
                    String locationCode = generateLocationCode(codePattern, row + 1, col + 1);
                    
                    // 计算库位的4个角点
                    List<Point> locationCorners = calculateLocationCorners(
                        corner1, lengthVectorX, lengthVectorY, widthVectorX, widthVectorY, 
                        col, row);
                    
                    locations.add(new ShelfLocationResult(locationCode, locationCorners));
                }
            }
            
        } catch (Exception e) {
            System.err.println("货架库位生成失败: " + e.getMessage());
            e.printStackTrace();
        }

        return locations;
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 为每个库位找到在线路上的锚点
     */
    private void findAnchorPointsForLocations(List<StorageLocation> locations, List<RouteSegment> routeSegments) {
        for (StorageLocation location : locations) {
            Point centerPoint = location.getCenterPoint();
            Point nearestAnchor = findNearestPointOnRoute(centerPoint, routeSegments);
            location.setAnchorPoint(nearestAnchor);
        }
    }

    /**
     * 在线路上找到距离目标点最近的点
     */
    private Point findNearestPointOnRoute(Point target, List<RouteSegment> routeSegments) {
        Point nearest = null;
        double minDistance = Double.MAX_VALUE;

        for (RouteSegment segment : routeSegments) {
            Point segmentNearest = segment.findNearestPoint(target);
            if (segmentNearest != null) {
                double distance = target.distanceTo(segmentNearest);
                if (distance < minDistance) {
                    minDistance = distance;
                    nearest = segmentNearest;
                }
            }
        }

        return nearest;
    }

    /**
     * 构建完整的线路图
     */
    private List<Point> buildCompleteRoute(List<RouteSegment> routeSegments) {
        List<Point> completeRoute = new ArrayList<>();

        for (RouteSegment segment : routeSegments) {
            if (completeRoute.isEmpty()) {
                completeRoute.addAll(segment.getPoints());
            } else {
                // 跳过重复的连接点
                List<Point> segmentPoints = segment.getPoints();
                if (!segmentPoints.isEmpty() &&
                    !completeRoute.get(completeRoute.size() - 1).equals(segmentPoints.get(0))) {
                    completeRoute.addAll(segmentPoints);
                } else if (segmentPoints.size() > 1) {
                    completeRoute.addAll(segmentPoints.subList(1, segmentPoints.size()));
                }
            }
        }

        return completeRoute;
    }

    /**
     * 使用贪心算法优化库位访问顺序（简化版TSP）
     */
    private List<String> optimizeLocationOrder(List<StorageLocation> locations, Point startPoint) {
        if (locations.isEmpty()) {
            return new ArrayList<>();
        }

        List<String> optimizedOrder = new ArrayList<>();
        Set<StorageLocation> unvisited = new HashSet<>(locations);
        Point currentPoint = startPoint;

        // 贪心算法：每次选择距离当前点最近的未访问库位
        while (!unvisited.isEmpty()) {
            StorageLocation nearest = null;
            double minDistance = Double.MAX_VALUE;

            for (StorageLocation location : unvisited) {
                Point anchorPoint = location.getAnchorPoint();
                if (anchorPoint != null) {
                    double distance = currentPoint.distanceTo(anchorPoint);
                    if (distance < minDistance) {
                        minDistance = distance;
                        nearest = location;
                    }
                }
            }

            if (nearest != null) {
                optimizedOrder.add(nearest.getCode());
                unvisited.remove(nearest);
                currentPoint = nearest.getAnchorPoint();
            } else {
                // 如果找不到有效的锚点，直接添加剩余的库位
                for (StorageLocation location : unvisited) {
                    optimizedOrder.add(location.getCode());
                }
                break;
            }
        }

        return optimizedOrder;
    }

    /**
     * 构建完整的路径坐标
     */
    private List<String> buildPathCoordinates(List<StorageLocation> locations,
                                            List<String> optimizedOrder,
                                            List<Point> completeRoute,
                                            Point startPoint) {
        List<String> pathCoordinates = new ArrayList<>();

        // 添加起始点
        pathCoordinates.add(startPoint.toString());

        Point currentPoint = startPoint;

        // 为每个库位构建路径
        for (String locationCode : optimizedOrder) {
            StorageLocation location = findLocationByCode(locations, locationCode);
            if (location != null && location.getAnchorPoint() != null) {
                // 在完整线路上找到从当前点到目标锚点的路径
                List<Point> segmentPath = findPathOnRoute(currentPoint, location.getAnchorPoint(), completeRoute);

                // 添加路径点（跳过起点避免重复）
                for (int i = 1; i < segmentPath.size(); i++) {
                    pathCoordinates.add(segmentPath.get(i).toString());
                }

                currentPoint = location.getAnchorPoint();
            }
        }

        return pathCoordinates;
    }

    /**
     * 在线路上找到两点间的路径
     */
    private List<Point> findPathOnRoute(Point start, Point end, List<Point> completeRoute) {
        List<Point> path = new ArrayList<>();

        // 找到起点和终点在线路上的索引
        int startIndex = findNearestIndexOnRoute(start, completeRoute);
        int endIndex = findNearestIndexOnRoute(end, completeRoute);

        if (startIndex == -1 || endIndex == -1) {
            // 如果找不到索引，直接连接
            path.add(start);
            path.add(end);
            return path;
        }

        // 构建路径
        path.add(start);

        if (startIndex <= endIndex) {
            for (int i = startIndex; i <= endIndex; i++) {
                path.add(completeRoute.get(i));
            }
        } else {
            for (int i = startIndex; i >= endIndex; i--) {
                path.add(completeRoute.get(i));
            }
        }

        return path;
    }

    /**
     * 找到点在线路上最近的索引
     */
    private int findNearestIndexOnRoute(Point target, List<Point> route) {
        int nearestIndex = -1;
        double minDistance = Double.MAX_VALUE;

        for (int i = 0; i < route.size(); i++) {
            double distance = target.distanceTo(route.get(i));
            if (distance < minDistance) {
                minDistance = distance;
                nearestIndex = i;
            }
        }

        return nearestIndex;
    }

    /**
     * 根据code查找库位
     */
    private StorageLocation findLocationByCode(List<StorageLocation> locations, String code) {
        return locations.stream()
                       .filter(loc -> loc.getCode().equals(code))
                       .findFirst()
                       .orElse(null);
    }

    /**
     * 计算路径总距离
     */
    private double calculateTotalDistance(List<String> pathCoordinates) {
        if (pathCoordinates.size() <= 1) {
            return 0.0;
        }

        double totalDistance = 0.0;
        for (int i = 0; i < pathCoordinates.size() - 1; i++) {
            Point current = new Point(pathCoordinates.get(i));
            Point next = new Point(pathCoordinates.get(i + 1));
            totalDistance += current.distanceTo(next);
        }

        return totalDistance;
    }

    /**
     * 生成库位编号
     */
    private String generateLocationCode(String pattern, int row, int col) {
        // 支持简单的模式替换，如 "A{row:02d}{col:02d}" -> "A0101"
        String result = pattern;
        result = result.replace("{row:02d}", String.format("%02d", row));
        result = result.replace("{col:02d}", String.format("%02d", col));
        result = result.replace("{row}", String.valueOf(row));
        result = result.replace("{col}", String.valueOf(col));
        return result;
    }

    /**
     * 计算库位的4个角点坐标
     */
    private List<Point> calculateLocationCorners(Point baseCorner,
                                               double lengthVectorX, double lengthVectorY,
                                               double widthVectorX, double widthVectorY,
                                               int col, int row) {
        List<Point> corners = new ArrayList<>();

        // 计算库位左下角的基准点
        double baseX = baseCorner.getX() + col * lengthVectorX + row * widthVectorX;
        double baseY = baseCorner.getY() + col * lengthVectorY + row * widthVectorY;

        // 计算4个角点（按逆时针顺序）
        corners.add(new Point(baseX, baseY)); // 左下角
        corners.add(new Point(baseX + lengthVectorX, baseY + lengthVectorY)); // 右下角
        corners.add(new Point(baseX + lengthVectorX + widthVectorX, baseY + lengthVectorY + widthVectorY)); // 右上角
        corners.add(new Point(baseX + widthVectorX, baseY + widthVectorY)); // 左上角

        return corners;
    }
}

package com.chinaservices.jasypt.util;

import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.symmetric.AES;

/**
 * <AUTHOR>
 * @date 2025/06/25
 **/
public class SimpleSecureAesUtil {

    private static final String SECRET_KEY = "1234567890123456";
    private static final AES aes = SecureUtil.aes(SECRET_KEY.getBytes());

    public static String encryptName(String name) {
        return aes.encryptHex(name);
    }

    public static String decryptName(String encryptedName) {
        return aes.decryptStr(encryptedName);
    }
}

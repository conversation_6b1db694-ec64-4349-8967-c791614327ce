package com.chinaservices.wms.sse.web;

import cn.hutool.core.util.StrUtil;
import com.chinaservices.sdk.support.result.ResponseData;
import com.chinaservices.session.SessionContext;
import com.chinaservices.wms.sse.common.SourceSystemEnum;
import com.chinaservices.wms.sse.service.SseConnectionManager;
import com.chinaservices.wms.sse.service.SseSubscriptionService;
import jakarta.servlet.http.HttpServletRequest;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.HashMap;
import java.util.Map;

/**
 * SSE 连接控制器
 */
@RestController
@RequestMapping("/api/sse")
@Slf4j
@AllArgsConstructor
public class CustomSseEmitterController {

    private final SseConnectionManager connectionManager;
    private final SseSubscriptionService subscriptionService;


    /**
     * 建立SSE连接 - 支持GET和POST请求，优先从headers获取参数
     */
    @CrossOrigin(originPatterns = "*", allowedHeaders = "*", allowCredentials = "true")
    @GetMapping(value = "/connect")
    public SseEmitter connect(HttpServletRequest request) {
        log.debug("收到SSE连接请求，方法: {}, URL: {}", request.getMethod(), request.getRequestURL());
        try {
            // 优先从headers获取参数，其次从query parameters
            String type = request.getHeader("type");
            if (StrUtil.isBlank(type)) {
                type = SourceSystemEnum.DEFAULT.getCode();
            }
            // 获取客户端标识（用于区分不同的客户端连接）
            String clientId = request.getHeader("X-Client-ID");
            if (StrUtil.isBlank(clientId)) {
                clientId = request.getParameter("clientId");
            }
            // 获取用户信息
            Long userId = SessionContext.getSessionUserInfo().getUserId();
            if (userId == null) {
                log.error("用户未登录，无法建立SSE连接");
                throw new RuntimeException("用户未登录，无法建立SSE连接");
            }
            // 解析来源系统
            SourceSystemEnum sourceSystem = SourceSystemEnum.getByCode(type);
            if (sourceSystem == null) {
                log.error("SSE连接失败，不支持的系统类型: {}", type);
                throw new RuntimeException("SSE连接失败，不支持的系统类型: " + type);
            }
            // 获取客户端信息
            String clientIp = getClientIp(request);
            String userAgent = request.getHeader("User-Agent");
            log.debug("收到SSE连接请求，用户ID: {}, 系统类型: {}, 客户端IP: {},  客户端ID: {}",
                userId, sourceSystem.getCode(), clientIp, clientId);
            SseEmitter emitter = connectionManager.createConnection(userId, sourceSystem, clientIp, userAgent);
            log.debug("SSE连接已建立，用户ID: {}, 系统类型: {}", userId, sourceSystem.getCode());
            return emitter;
        } catch (Exception e) {
            log.error("SSE连接建立失败: {}", e.getMessage(), e);
            throw new RuntimeException("SSE连接建立失败: " + e.getMessage());
        }
    }

    /**
     * 订阅业务
     */
    @CrossOrigin(originPatterns = "*", allowedHeaders = "*", allowCredentials = "true")
    @PostMapping("/subscribe")
    public ResponseData subscribe(HttpServletRequest request) {
        try {
            String connectionId = request.getParameter("connectionId");
            String businessType = request.getParameter("businessType");
            if (StrUtil.isBlank(connectionId) || StrUtil.isBlank(businessType)) {
                throw new IllegalArgumentException("连接ID和业务代码不能为空");
            }
            subscriptionService.subscribe(connectionId, businessType);
            log.info("业务订阅成功，连接ID: {},  业务代码: {}", connectionId, businessType);
            return ResponseData.success(true);
        } catch (Exception e) {
            return ResponseData.success(false);
        }
    }

    /**
     * 取消订阅业务
     */
    @CrossOrigin(originPatterns = "*", allowedHeaders = "*", allowCredentials = "true")
    @PostMapping("/unsubscribe")
    public ResponseData unsubscribe(HttpServletRequest request) {
        try {
            String connectionId = request.getParameter("connectionId");
            String businessType = request.getParameter("businessType");
            if (StrUtil.isBlank(connectionId) || StrUtil.isBlank(businessType)) {
                throw new IllegalArgumentException("连接ID和业务代码不能为空");
            }
            subscriptionService.unsubscribe(connectionId, businessType);
            log.info("取消业务订阅成功，连接ID: {}, 业务代码: {}", connectionId, businessType);
            return ResponseData.success(true);
        } catch (Exception e) {
            return ResponseData.success(false);
        }
    }

    /**
     * 断开SSE连接
     */
    @CrossOrigin(originPatterns = "*", allowedHeaders = "*", allowCredentials = "true")
    @PostMapping("/disconnect")
    public void disconnect(@RequestParam(required = false) String type) {
        try {
            Long userId = SessionContext.getSessionUserInfo().getUserId();
            if (userId == null) {
                throw new RuntimeException("用户未登录");
            }
            // 解析系统类型
            if (StrUtil.isBlank(type)) {
                type = SourceSystemEnum.DEFAULT.getCode();
            }
            SourceSystemEnum sourceSystem = SourceSystemEnum.getByCode(type);
            if (sourceSystem == null) {
                throw new RuntimeException("不支持的系统类型: " + type);
            }
            // 关闭连接
            connectionManager.closeUserConnection(userId, sourceSystem);
            log.info("用户主动断开SSE连接，用户ID: {}, 系统类型: {}", userId, sourceSystem.getCode());
        } catch (Exception e) {
            log.error("断开SSE连接失败，错误: {}", e.getMessage(), e);
            throw new RuntimeException("断开SSE连接失败: " + e.getMessage());
        }
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (StrUtil.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (StrUtil.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (StrUtil.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }
}

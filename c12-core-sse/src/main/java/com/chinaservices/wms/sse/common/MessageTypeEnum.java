package com.chinaservices.wms.sse.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 消息类型枚举
 */
@Getter
@AllArgsConstructor
public enum MessageTypeEnum {
    
    /**
     * 心跳消息
     */
    HEARTBEAT("heartbeat", "心跳消息"),

    /**
     * 业务消息
     */
    BUSINESS("business", "业务消息"),

    /**
     * 业务消息
     */
    CONNECTION("connection", "链接消息"),

    /**
     * 系统消息
     */
    SYSTEM("system", "系统消息"),

    /**
     * 默认消息
     */
    DEFAULT("default", "默认消息");
    
    private final String code;
    private final String description;
    
    /**
     * 根据代码获取枚举
     */
    public static MessageTypeEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (MessageTypeEnum typeEnum : values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum;
            }
        }
        return null;
    }
}

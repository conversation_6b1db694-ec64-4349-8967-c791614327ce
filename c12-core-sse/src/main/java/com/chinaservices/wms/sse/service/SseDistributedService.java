package com.chinaservices.wms.sse.service;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.chinaservices.core.redis.util.CustomRedis;
import com.chinaservices.wms.sse.common.MessageTypeEnum;
import com.chinaservices.wms.sse.common.SseConstants;
import com.chinaservices.wms.sse.model.SseMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.redisson.api.RedissonClient;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * SSE 分布式服务
 * 使用 RedissonClient 实现跨节点消息广播
 */
@Slf4j
public class SseDistributedService {

    /**
     * Redis 频道名称
     */
    private static final String SSE_CHANNEL = "sse:broadcast";
    private final RedissonClient redissonClient;
    private final SseMessageService messageService;

    public SseDistributedService(RedissonClient redissonClient, SseMessageService messageService) {
        this.redissonClient = redissonClient;
        this.messageService = messageService;
    }

    /**
     * 分布式发送消息给指定用户
     */
    public void sendMessageToUser(Long userId, String data, MessageTypeEnum messageType, String businessType) {
        SseMessage sseMessage = SseMessage.builder().messageId(IdUtil.fastSimpleUUID()).messageType(messageType).businessType(businessType).data(data).userIds(List.of(userId)).createTime(LocalDateTime.now()).build();
        sseMessage.setUserIds(Lists.newArrayList());
        sseMessage.setMessageType(null);
        publishMessage(sseMessage);
    }

    /**
     * 分布式发送消息给多个用户
     */
    public void sendMessageToUsers(List<Long> userIds, String data, MessageTypeEnum messageType, String businessType) {
        SseMessage sseMessage = SseMessage.builder().messageId(IdUtil.fastSimpleUUID()).messageType(messageType)
                .businessType(businessType).data(data).userIds(userIds).createTime(LocalDateTime.now()).build();
        sseMessage.setUserIds(Lists.newArrayList());
        sseMessage.setMessageType(null);
        publishMessage(sseMessage);
    }

    /**
     * 分布式广播消息给所有连接
     */
    public void broadcastMessage(String data, MessageTypeEnum messageType, String businessType) {
        SseMessage sseMessage = SseMessage.builder().messageId(IdUtil.fastSimpleUUID()).messageType(messageType).businessType(businessType).data(data).createTime(LocalDateTime.now()).build();
        sseMessage.setUserIds(Lists.newArrayList());
        sseMessage.setMessageType(null);
        publishMessage(sseMessage);
    }

    /**
     * 分布式发送业务消息
     */
    public void sendBusinessMessage(String bizCode, String data, String businessType) {
        try {
            String bizKey = SseConstants.BIZ_SUBSCRIPTION_PREFIX + bizCode;
            Set<Object> subscribers = CustomRedis.sMembers(bizKey);
            if (subscribers == null || subscribers.isEmpty()) {
                log.info("业务无订阅连接，不推送，业务代码: {}", bizCode);
                return;
            }
            // 解析连接标识，提取用户ID
            Set<Long> userIds = new HashSet<>();
            for (Object subscriber : subscribers) {
                String connectionKey = String.valueOf(subscriber);
                try {
                    String[] parts = connectionKey.split(":");
                    if (parts.length >= 3) {
                        Long userId = Long.valueOf(parts[2]);
                        userIds.add(userId);
                    }
                } catch (Exception e) {
                    log.warn("解析连接标识失败，跳过: {}, 错误: {}", connectionKey, e.getMessage());
                }
            }
            if (!userIds.isEmpty()) {
                sendMessageToUsers(new ArrayList<>(userIds), data, MessageTypeEnum.DEFAULT, businessType);
            } else {
                log.warn("没有有效的用户ID，业务代码: {}", bizCode);
            }
        } catch (Exception e) {
            log.error("发送业务消息失败，业务代码: {}, 错误: {}", bizCode, e.getMessage(), e);
        }
    }

    /**
     * 发布消息到 Redis 频道
     */
    private void publishMessage(SseMessage sseMessage) {
        try {
            sseMessage.setUserIds(null);
            sseMessage.setMessageType(null);
            String messageJson = JSONUtil.toJsonStr(sseMessage);
            redissonClient.getTopic(SSE_CHANNEL).publishAsync(messageJson);
        } catch (Exception e) {
            log.error("发布消息到Redis失败，消息ID: {}, 错误: {}", sseMessage.getMessageId(), e.getMessage(), e);
        }
    }

    /**
     * 处理接收到的分布式消息
     * 此方法由 Redis 订阅监听器调用
     */
    public void handleDistributedMessage(String messageJson) {
        try {
            SseMessage sseMessage = JSON.parseObject(messageJson, SseMessage.class);
            // 根据消息类型处理
            if (sseMessage.getUserIds() != null && !sseMessage.getUserIds().isEmpty()) {
                messageService.sendMessageToUsers(sseMessage.getUserIds(), sseMessage.getData(), sseMessage.getMessageType(), sseMessage.getBusinessType());
            } else {
                // 广播消息
                messageService.broadcastMessage(sseMessage.getData(), sseMessage.getMessageType(), sseMessage.getBusinessType());
            }
        } catch (Exception e) {
            log.error("处理分布式消息失败，消息内容: {}, 错误: {}", messageJson, e.getMessage(), e);
        }
    }
}

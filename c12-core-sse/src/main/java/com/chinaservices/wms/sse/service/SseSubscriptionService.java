package com.chinaservices.wms.sse.service;

import com.chinaservices.core.redis.util.CustomRedis;
import com.chinaservices.wms.sse.common.SseConstants;
import com.chinaservices.wms.sse.common.SourceSystemEnum;
import com.chinaservices.wms.sse.model.SseConnection;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.Set;

/**
 * SSE 订阅服务 - 分布式版本
 * 使用 CustomRedis 管理基于连接的订阅关系，支持多节点部署
 * 支持同一用户多个连接（PC端、APP端等）的独立订阅管理
 */
@Slf4j
@AllArgsConstructor
public class SseSubscriptionService {

    private final SseConnectionManager sseConnectionManager;
    /**
     * 连接订阅业务（基于连接ID的订阅）
     */
    public void subscribe(String connectionId, String bizCode) {
        SseConnection sseConnection = sseConnectionManager.getSseConnection(connectionId);
        try {
            // 构建连接标识：connectionId:sourceSystem:userId
            String connectionKey = buildConnectionKey(connectionId, sseConnection.getSourceSystem(), sseConnection.getUserId());
            // 添加到业务订阅集合 (bizCode -> Set<connectionKey>)
            String bizKey = SseConstants.BIZ_SUBSCRIPTION_PREFIX + bizCode;
            CustomRedis.sAdd(bizKey, connectionKey);

            // 添加到连接订阅集合 (connectionKey -> Set<bizCode>)
            String connSubscriptionKey = SseConstants.USER_CONNECTION_PREFIX + "conn_subscriptions:" + connectionKey;
            CustomRedis.sAdd(connSubscriptionKey, bizCode);

            log.info("连接订阅业务成功，连接ID: {}, 用户ID: {}, 系统类型: {}, 业务代码: {}",
                connectionId, sseConnection.getUserId(), sseConnection.getSourceSystem().getCode(), bizCode);
        } catch (Exception e) {
            log.error("连接订阅业务失败，连接ID: {}, 用户ID: {}, 系统类型: {}, 业务代码: {}, 错误: {}",
                connectionId, sseConnection.getUserId(), sseConnection.getSourceSystem().getCode(), bizCode, e.getMessage(), e);
            throw new RuntimeException("订阅业务失败: " + e.getMessage());
        }
    }

    /**
     * 连接取消订阅业务
     */
    public void unsubscribe(String connectionId, String businessType) {
        SseConnection sseConnection = sseConnectionManager.getSseConnection(connectionId);
        try {
            String connectionKey = buildConnectionKey(connectionId, sseConnection.getSourceSystem(), sseConnection.getUserId());
            String bizKey = SseConstants.BIZ_SUBSCRIPTION_PREFIX + businessType;
            CustomRedis.sRem(bizKey, connectionKey);
            String connSubscriptionKey = SseConstants.USER_CONNECTION_PREFIX + "conn_subscriptions:" + connectionKey;
            CustomRedis.sRem(connSubscriptionKey, businessType);
            log.info("连接取消订阅业务成功，连接ID: {}, 用户ID: {}, 系统类型: {}, 业务代码: {}",
                connectionId, sseConnection.getUserId(), sseConnection.getSourceSystem(), businessType);
        } catch (Exception e) {
            log.error("连接取消订阅业务失败，连接ID: {}, 用户ID: {}, 系统类型: {}, 业务代码: {}, 错误: {}",
                connectionId, sseConnection.getUserId(), sseConnection.getSourceSystem(), businessType, e.getMessage(), e);
        }
    }

    /**
     * 构建连接标识
     */
    private String buildConnectionKey(String connectionId, SourceSystemEnum sourceSystem, Long userId) {
        return connectionId + ":" + sourceSystem.getCode() + ":" + userId;
    }
}

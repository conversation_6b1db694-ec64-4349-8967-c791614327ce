package com.chinaservices.wms.sse.config;

import com.chinaservices.wms.sse.listener.SseRedisMessageListener;
import com.chinaservices.wms.sse.sees.SendEventSSE;
import com.chinaservices.wms.sse.service.*;
import com.chinaservices.wms.sse.web.CustomSseEmitterController;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;

import static com.chinaservices.wms.sse.common.SseConstants.SSE_CHANNEL;


/**
 * SSE 配置类
 */
@Configuration
@ConditionalOnProperty(
        name = "chinaservices.sse.enable",
        havingValue = "true",
        matchIfMissing = true
)
@EnableScheduling
@Slf4j
public class SseConfig {


    /**
     * SSE 连接管理器
     */
    @Bean
    public SseConnectionManager sseConnectionManager() {
        return new SseConnectionManager();
    }

    /**
     * SSE 订阅服务
     */
    @Bean
    public SseSubscriptionService sseSubscriptionService(SseConnectionManager connectionManager) {
        return new SseSubscriptionService(connectionManager);
    }

    /**
     * SSE 消息服务
     */
    @Bean
    public SseMessageService sseMessageService(SseConnectionManager connectionManager) {
        return new SseMessageService(connectionManager);
    }

    /**
     * SSE 分布式服务
     */
    @Bean
    public SseDistributedService sseDistributedService(RedissonClient redissonClient, SseMessageService messageService) {
        return new SseDistributedService(redissonClient, messageService);
    }

    /**
     * SSE Redis 消息监听器
     */
    @Bean
    public SseRedisMessageListener sseRedisMessageListener(SseDistributedService distributedService) {
        return new SseRedisMessageListener(distributedService);
    }

    /**
     * SSE 心跳服务
     */
    @Bean
    public SseHeartbeatService sseHeartbeatService(SseMessageService messageService, SseConnectionManager connectionManager) {
        return new SseHeartbeatService(messageService, connectionManager);
    }

    /**
     * SSE 事件处理器
     */
    @Bean
    public SendEventSSE sendEventSSE(SseDistributedService distributedService) {
        return new SendEventSSE(distributedService);
    }

    /**
     * SSE 连接控制器
     */
    @Bean
    public CustomSseEmitterController customSseEmitterController(
            SseConnectionManager connectionManager,
            SseSubscriptionService subscriptionService) {
        return new CustomSseEmitterController(connectionManager, subscriptionService);
    }

    /**
     * SSE Redis 监听器初始化
     */
    @Bean
    public SseRedisListenerInitializer sseRedisListenerInitializer(
            RedissonClient redissonClient,
            SseRedisMessageListener messageListener) {
        return new SseRedisListenerInitializer(redissonClient, messageListener);
    }

    /**
     * SSE 模块初始化
     */
    @PostConstruct
    public void init() {
        log.info("SSE分布式模块配置完成");
    }

    /**
     * Redis 监听器初始化器
     */
    @Slf4j
    public static class SseRedisListenerInitializer {
        private final RedissonClient redissonClient;
        private final SseRedisMessageListener messageListener;

        public SseRedisListenerInitializer(RedissonClient redissonClient, SseRedisMessageListener messageListener) {
            this.redissonClient = redissonClient;
            this.messageListener = messageListener;
            init();
        }

        private void init() {
            try {
                redissonClient.getTopic(SSE_CHANNEL).addListener(String.class, messageListener);
                log.info("SSE分布式模块初始化完成，监听频道: {}", SSE_CHANNEL);
            } catch (Exception e) {
                log.error("SSE分布式模块初始化失败: {}", e.getMessage(), e);
            }
        }
    }
}

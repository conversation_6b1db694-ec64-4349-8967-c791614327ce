package com.chinaservices.wms.sse.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 来源系统枚举
 */
@Getter
@AllArgsConstructor
public enum SourceSystemEnum {
    
    /**
     * PC端
     */
    PC("pc", "PC端"),
    
    /**
     * 移动端
     */
    APP("app", "移动端"),

    /**
     * 默认
     */
    DEFAULT("default", "默认PC");
    
    private final String code;
    private final String description;
    
    /**
     * 根据代码获取枚举
     */
    public static SourceSystemEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (SourceSystemEnum systemEnum : values()) {
            if (systemEnum.getCode().equalsIgnoreCase(code)) {
                return systemEnum;
            }
        }
        return null;
    }
}

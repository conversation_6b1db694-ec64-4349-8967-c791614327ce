package com.chinaservices.wms.sse.sees;

import cn.hutool.core.collection.CollUtil;
import com.chinaservices.wms.sse.model.LargeScreenDTO;
import com.chinaservices.wms.sse.service.SseDistributedService;
import com.google.common.eventbus.Subscribe;
import lombok.extern.slf4j.Slf4j;

/**
 * SSE 事件处理器 - 分布式版本
 */
@Slf4j
public class SendEventSSE {

    private final SseDistributedService distributedService;

    public SendEventSSE(SseDistributedService distributedService) {
        this.distributedService = distributedService;
    }

    /**
     * 大屏订阅模型 消息发送
     *
     * @param dto 订阅模型
     */
    @Subscribe
    public void sendSseMessageLar(LargeScreenDTO dto) {
        dto.setData(dto.getIndexCodeEnums().getCode());
        // 构建消息内容
        if (CollUtil.isNotEmpty(dto.getUserId())) {
            distributedService.sendMessageToUsers(dto.getUserId(), dto.getData(), dto.getMessageType(), dto.getIndexCodeEnums().getType());
            return;
        }
        // 使用分布式服务发送业务消息
        distributedService.sendBusinessMessage(dto.getIndexCodeEnums().getCode(), dto.getData(), dto.getIndexCodeEnums().getType());
    }
}

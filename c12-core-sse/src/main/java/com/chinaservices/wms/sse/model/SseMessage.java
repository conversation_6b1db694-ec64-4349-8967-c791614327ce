package com.chinaservices.wms.sse.model;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.chinaservices.util.StringPool;
import com.chinaservices.wms.sse.common.MessageTypeEnum;
import com.chinaservices.wms.sse.common.SourceSystemEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * SSE 消息模型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SseMessage implements Serializable {
    
    /**
     * 消息ID
     */
    private String messageId;
    
    /**
     * 消息类型
     */
    private MessageTypeEnum messageType;

    /**
     * 业务类型
     */
    private String businessType;
    
    /**
     * 目标用户ID列表
     */
    private List<Long> userIds;
    
    /**
     * 来源系统
     */
    private SourceSystemEnum sourceSystem;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 指令数据
     */
    private String data;

    private String connectionId;

    public MessageTypeEnum getMessageType() {
        if(ObjUtil.isNull(messageType)){
            messageType = MessageTypeEnum.DEFAULT;
        }
        return messageType;
    }


    public String getBusinessType(){
        if(StringUtils.isEmpty(businessType)){
            businessType = StringPool.EMPTY;
        }
        return businessType;
    }
}

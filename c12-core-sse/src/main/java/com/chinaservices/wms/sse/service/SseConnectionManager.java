package com.chinaservices.wms.sse.service;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.chinaservices.wms.sse.common.MessageTypeEnum;
import com.chinaservices.wms.sse.common.SourceSystemEnum;
import com.chinaservices.wms.sse.common.SseConstants;
import com.chinaservices.wms.sse.model.SseConnection;
import com.chinaservices.wms.sse.model.SseMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;

/**
 * SSE 连接管理器
 */
@Slf4j
public class SseConnectionManager {

    /**
     * 用户连接映射 userId -> List<SseConnection>
     */
    private final Map<Long, List<SseConnection>> userConnections = new ConcurrentHashMap<>();

    /**
     * 连接ID映射 connectionId -> SseConnection
     */
    private final Map<String, SseConnection> connectionMap = new ConcurrentHashMap<>();


    /**
     * 创建SSE连接（完整版本）
     */
    public SseEmitter createConnection(Long userId, SourceSystemEnum sourceSystem, String clientIp, String userAgent) {
        try {
            // 创建新的SSE发射器
            SseEmitter emitter = new SseEmitter(SseConstants.CONNECTION_TIMEOUT);
            // 生成连接ID
            String connectionId = IdUtil.fastSimpleUUID();
            String sessionId  = IdUtil.fastSimpleUUID();
            // 创建连接信息
            SseConnection connection = SseConnection.builder().connectionId(connectionId).sessionId(sessionId).userId(userId).sourceSystem(sourceSystem).emitter(emitter).connectTime(LocalDateTime.now()).lastActiveTime(LocalDateTime.now()).active(true).clientIp(clientIp).userAgent(userAgent).build();
            emitter.onCompletion(() -> handleConnectionCompletion(connectionId, userId));
            emitter.onTimeout(() -> handleConnectionTimeout(connectionId, userId));
            emitter.onError(throwable -> handleConnectionError(connectionId, userId, throwable));
            connectionMap.put(connectionId, connection);
            userConnections.computeIfAbsent(userId, k -> new CopyOnWriteArrayList<>()).add(connection);
            log.info("SSE连接创建成功，连接ID: {}, 会话ID: {}, 用户ID: {}, 来源系统: {}", connectionId, sessionId, userId, sourceSystem.getCode());
            sendConnectionConfirmation(emitter, connectionId);
            return emitter;
        } catch (Exception e) {
            log.error("创建SSE连接失败，用户ID: {}, 来源系统: {}, 错误: {}", userId, sourceSystem.getCode(), e.getMessage(), e);
            throw new RuntimeException("创建SSE连接失败: " + e.getMessage());
        }
    }

    /**
     * 移除连接
     */
    public void removeConnection(String connectionId) {
        try {
            SseConnection connection = connectionMap.remove(connectionId);
            if (connection != null) {
                connection.setActive(false);
                List<SseConnection> connections = userConnections.get(connection.getUserId());
                if (connections != null) {
                    connections.removeIf(conn -> connectionId.equals(conn.getConnectionId()));
                    if (connections.isEmpty()) {
                        userConnections.remove(connection.getUserId());
                    }
                }
                log.info("SSE连接已移除，连接ID: {}, 用户ID: {}", connectionId, connection.getUserId());
            }
        } catch (Exception e) {
            log.error("移除SSE连接失败，连接ID: {}, 错误: {}", connectionId, e.getMessage(), e);
        }
    }

    public void removeConnection(String connectionId,Long userId) {
        try {
            connectionMap.remove(connectionId);
            userConnections.remove(userId);
        } catch (Exception e) {
            log.error("移除SSE连接失败，连接ID: {}, 错误: {}", connectionId, e.getMessage(), e);
        }
    }

    /**
     * 获取用户的所有活跃连接
     */
    public List<SseConnection> getUserConnections(Long userId) {
        List<SseConnection> connections = userConnections.get(userId);
        if (connections == null) {
            return List.of();
        }
        return connections.stream().filter(SseConnection::getActive).collect(Collectors.toList());
    }

    /**
     * 获取所有活跃连接
     */
    public List<SseConnection> getAllActiveConnections() {
        return connectionMap.values().stream().filter(SseConnection::getActive).collect(Collectors.toList());
    }

    /**
     * 清理过期连接
     */
    public void cleanExpiredConnections() {
        LocalDateTime expireTime = LocalDateTime.now().minusMinutes(30);
        List<String> expiredConnectionIds = connectionMap.values().stream().filter(conn -> conn.getLastActiveTime().isBefore(expireTime)).map(SseConnection::getConnectionId).toList();
        expiredConnectionIds.forEach(this::removeConnection);
        if (!expiredConnectionIds.isEmpty()) {
            log.info("清理过期SSE连接，数量: {}", expiredConnectionIds.size());
        }
    }

    /**
     * 更新连接活跃时间
     */
    public void updateLastActiveTime(String connectionId) {
        SseConnection connection = connectionMap.get(connectionId);
        if (connection != null) {
            connection.setLastActiveTime(LocalDateTime.now());
        }
    }

    /**
     * 检查连接健康状态
     */
    public boolean isConnectionHealthy(String connectionId) {
        SseConnection connection = connectionMap.get(connectionId);
        if (connection == null || !connection.getActive()) {
            return false;
        }

        // 检查连接是否在合理时间内有活动
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime lastActive = connection.getLastActiveTime();
        long minutesSinceLastActive = java.time.Duration.between(lastActive, now).toMinutes();

        // 如果超过2分钟没有活动，认为连接可能有问题
        boolean isHealthy = minutesSinceLastActive <= 2;

        if (!isHealthy) {
            log.warn("连接健康检查失败，连接ID: {}, 最后活跃时间: {}, 距今: {}分钟", connectionId, lastActive, minutesSinceLastActive);
        }

        return isHealthy;
    }

    /**
     * 获取不健康的连接列表
     */
    public List<SseConnection> getUnhealthyConnections() {
        return connectionMap.values().stream().filter(conn -> conn.getActive() && !isConnectionHealthy(conn.getConnectionId())).collect(Collectors.toList());
    }


    /**
     * 查找现有的活跃连接（相同用户和系统类型）
     */
    private SseConnection findExistingConnection(Long userId, SourceSystemEnum sourceSystem) {
        List<SseConnection> connections = userConnections.get(userId);
        if (connections == null || connections.isEmpty()) {
            return null;
        }
        return connections.stream().filter(SseConnection::getActive).filter(conn -> sourceSystem.equals(conn.getSourceSystem())).findFirst().orElse(null);
    }

    /**
     * 关闭用户指定系统类型的连接
     */
    public void closeUserConnection(Long userId, SourceSystemEnum sourceSystem) {
        SseConnection existingConnection = findExistingConnection(userId, sourceSystem);
        if (existingConnection != null) {
            try {
                existingConnection.getEmitter().complete();
                log.info("主动关闭SSE连接，连接ID: {}, 用户ID: {}, 系统类型: {}", existingConnection.getConnectionId(), userId, sourceSystem.getCode());
            } catch (Exception e) {
                log.error("关闭SSE连接失败，连接ID: {}, 错误: {}", existingConnection.getConnectionId(), e.getMessage(), e);
            }
        }
    }

    /**
     * 处理连接正常完成
     */
    private void handleConnectionCompletion(String connectionId, Long userId) {
        log.debug("open完成--》{} userId:{}",connectionId,userId);
    }

    /**
     * 处理连接超时
     */
    private void handleConnectionTimeout(String connectionId, Long userId) {
        log.info("链接超时移除--》{} userId:{}",connectionId,userId);
        removeConnection(connectionId,userId);
    }

    /**
     * 处理连接错误（增强版，区分错误类型）
     */
    private void handleConnectionError(String connectionId, Long userId, Throwable throwable) {
        log.info("连接错误移除--》{} userId:{},异常：{}",connectionId,userId, throwable.getMessage());
        removeConnection(connectionId,userId);
    }

    /**
     * 发送连接确认消息（简化版）
     */
    private void sendConnectionConfirmation(SseEmitter emitter, String connectionId) {
        try {
            // 发送简单的连接确认消息
            SseMessage sseMessage = new SseMessage();
            sseMessage.setMessageType(MessageTypeEnum.CONNECTION);
            sseMessage.setMessageId(IdUtil.fastSimpleUUID());
            sseMessage.setData("连接成功");
            sseMessage.setCreateTime(LocalDateTime.now());
            sseMessage.setConnectionId(connectionId);
            emitter.send(SseEmitter.event().id(sseMessage.getMessageId())
                    .name(MessageTypeEnum.CONNECTION.getCode()).data(JSONUtil.toJsonStr(sseMessage)));
            log.debug("SSE连接确认消息已发送，连接ID: {}", connectionId);
        } catch (Exception e) {
            log.error("发送SSE连接确认消息失败，连接ID: {}, 错误: {}", connectionId, e.getMessage(), e);
        }
    }

    public SseConnection getSseConnection(String connectionId){
        SseConnection sseConnection =  connectionMap.get(connectionId);
        if(sseConnection == null){
            throw new RuntimeException("连接不存在");
        }
        return sseConnection;
    }
}

package com.chinaservices.wms.sse.model;

import com.chinaservices.wms.sse.common.SourceSystemEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * SSE 连接信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SseConnection implements Serializable {
    
    /**
     * 连接ID
     */
    private String connectionId;

    /**
     * 会话ID（用于区分同一用户的不同会话）
     */
    private String sessionId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 来源系统
     */
    private SourceSystemEnum sourceSystem;
    
    /**
     * SSE发射器
     */
    private transient SseEmitter emitter;
    
    /**
     * 连接时间
     */
    private LocalDateTime connectTime;
    
    /**
     * 最后活跃时间
     */
    private LocalDateTime lastActiveTime;
    
    /**
     * 是否活跃
     */
    private Boolean active;
    
    /**
     * 客户端IP
     */
    private String clientIp;
    
    /**
     * 用户代理
     */
    private String userAgent;
}

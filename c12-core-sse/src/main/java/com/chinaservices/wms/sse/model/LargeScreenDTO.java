package com.chinaservices.wms.sse.model;

import com.chinaservices.wms.sse.common.MessageTypeEnum;
import com.chinaservices.wms.sse.common.SourceSystemEnum;
import com.chinaservices.wms.sse.enums.IndexCodeEnums;
import lombok.*;

import java.util.List;

/**
 * 大屏相关消息体
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LargeScreenDTO {
    /**
     * 业务类型
     */
    IndexCodeEnums indexCodeEnums;
    /**
     * 绑定userid
     */
    List<Long> userId;
    /**
     * 端类型
     */
    SourceSystemEnum sourceSystemEnum;
    /**
     * 消息类型
     */
    MessageTypeEnum messageType;
    /**
     * String data  数据
     */
    String data;

    public SourceSystemEnum getSourceSystemEnum() {
        if (sourceSystemEnum == null) {
            sourceSystemEnum = SourceSystemEnum.DEFAULT;
        }
        return sourceSystemEnum;
    }

    public MessageTypeEnum getMessageType() {
        if (messageType == null) {
            messageType = MessageTypeEnum.DEFAULT;
        }
        return messageType;
    }
}

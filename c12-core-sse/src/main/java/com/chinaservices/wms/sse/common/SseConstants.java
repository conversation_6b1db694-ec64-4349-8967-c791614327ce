package com.chinaservices.wms.sse.common;

/**
 * SSE 常量类
 */
public class SseConstants {
    
    /**
     * 连接超时时间（毫秒）
     * 调整为5分钟，减少长时间无响应连接的影响
     */
    public static final long CONNECTION_TIMEOUT = 5 * 60 * 1000L; // 5分钟
    
    /**
     * 心跳间隔时间（毫秒）
     */
    public static final long HEARTBEAT_INTERVAL = 30 * 1000L; // 30秒
    
    /**
     * Redis 缓存前缀
     */
    public static final String REDIS_PREFIX = "sse:";
    
    /**
     * 用户连接缓存前缀
     */
    public static final String USER_CONNECTION_PREFIX = REDIS_PREFIX + "user:";
    
    /**
     * 业务订阅缓存前缀
     */
    public static final String BIZ_SUBSCRIPTION_PREFIX = REDIS_PREFIX + "biz:";

    /**
     * Redis 频道名称
     */
    public static final String SSE_CHANNEL = "sse:broadcast";
}

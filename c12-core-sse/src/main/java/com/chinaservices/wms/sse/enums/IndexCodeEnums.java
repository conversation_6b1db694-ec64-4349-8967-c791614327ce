package com.chinaservices.wms.sse.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 大屏指标code枚举
 */
@Getter
@AllArgsConstructor
public enum IndexCodeEnums {

    // 动态仓储监控-数据总览 盘点监控大屏-库位总览
    TOTAL_STOCK_AMOUNT("10001", "总库存量", "warehouse_screen,inventory_screen"),
    STOCK_CATEGORY_COUNT("10002", "库存品类", "warehouse_screen,inventory_screen"),
    STOCK_GOODS_AMOUNT("10003", "库存金额/库存商品金额", "warehouse_screen,inventory_screen"),
    AVG_STOCK_CYCLE("10004", "平均库存周期", "warehouse_screen"),
    MAX_CYCLE("10005", "最长周期", "warehouse_screen"),
    MIN_CYCLE("10006", "最短周期", "warehouse_screen"),

    // 动态仓储监控-库龄统计
    STOCK_AGE_STATS("10007", "库龄统计", "warehouse_screen"),

    // 动态仓储监控-库位分析 仓储位监控大屏-库位总览 货架分屏-库位总览 盘点监控大屏-库位总览
    TOTAL_LOC_COUNT("10008", "总库位", "warehouse_screen,zone_screen,inventory_screen"),
    OCCUPIED_LOC_COUNT("10009", "占用库位", "warehouse_screen,zone_screen,shelf_screen,inventory_screen"),
    VACANT_LOC_COUNT("10010", "空置库位/空闲库位", "warehouse_screen,zone_screen,shelf_screen,inventory_screen"),
    LOC_UTILIZATION_RATE("10011", "库位利用率/库位占用率", "warehouse_screen,zone_screen,shelf_screen,inventory_screen"),
    WAREHOUSE_ZONE_STATS("10012", "库区统计", "warehouse_screen"),

    // 动态仓储监控-出入库统计
    DAILY_INBOUND_AMOUNT("10013", "当日入库量", "warehouse_screen"),
    DAILY_OUTBOUND_AMOUNT("10014", "当日出库量", "warehouse_screen"),
    DAILY_DIRECT_DELIVERY("10015", "当日直发量", "warehouse_screen"),
    DAILY_RETURN_AMOUNT("10016", "当日退货量", "warehouse_screen"),
    CURRENT_STOCK_TOTAL_LOC("10017", "当前库存量", "warehouse_screen"),

    // 仓储位监控大屏-库位总览
    PENDING_PUTAWAY("10018", "待上架", "zone_screen"),
    PENDING_PICKING("10019", "待拣货", "zone_screen"),

    // 仓储位监控大屏-各货架库位情况
    SHELF_LOC_STATS("10020", "各货架库位情况", "zone_screen"),

    // 仓储位监控大屏-最近作业 库位作业大屏-最近作业
    PUTAWAY_PICKING_INFO("10021", "最近作业", "zone_screen,zone_work_screen"),

    // 仓储位监控大屏-库位作业情况 库位作业大屏-库位作业情况
    TOP_SHELF_BOTTOM_LOC("10022", "库位作业情况", "zone_screen,zone_work_screen"),

    // 货架/库位标识看板-待拣货 库位作业大屏-待拣货
    PICKING_ORDER_GOODS_INFO("10023", "待拣货", "shelf_screen,zone_work_screen"),

    // 货架/库位标识看板-货架看板
    LOC_GOODS_INFO_OR_VACANT_DAYS("10024", "货架看板", "shelf_screen"),

    // 库位作业大屏-待上架
    PUTAWAY_ORDER_GOODS_INFO("10025", "待上架", "zone_work_screen"),

    // 库位作业大屏-平均上架时效
    AVG_PUTAWAY_EFFICIENCY("10026", "平均上架时效", "zone_work_screen"),

    // 库位作业大屏-平均拣货时效
    AVG_PICKING_EFFICIENCY("10027", "平均拣货时效", "zone_work_screen"),

    // 盘点监控大屏-盘点情况
    INVENTORY_STATUS("10028", "盘点状态", "inventory_screen"),
    LAST_INVENTORY_STATUS("10029", "上次盘点情况", "inventory_screen"),

    // 盘点监控大屏-盘点情无源自动出入库
    PASSIVE_AUTH_IN_OUT("10030", "无源自动出入库", "inventory_screen"),

    // 盘点监控大屏-盘存百分比
    INVENTORY_COMPLETION_PERCENTAGE("10031", "盘存百分比", "inventory_screen"),

    // 盘点监控大屏-应盘数量
    EXPECTED_COUNT("10032", "应盘数量", "inventory_screen"),

    // 盘点监控大屏-实盘数量
    ACTUAL_COUNT("10033", "实盘数量", "inventory_screen"),

    // 盘点监控大屏-未盘数量
    UNCOUNTED_QUANTITY("10034", "未盘数量", "inventory_screen"),

    // 盘点监控大屏-盘点记录
    INVENTORY_RECORDS("10035", "盘点记录", "inventory_screen"),
    ;

    private final String code;
    private final String name;
    private final String type;

    /**
     * 通过code获取type
     *
     * @param code 指标代码
     * @return type 类型，如果未找到返回null
     */
    public static String getTypeByCode(String code) {
        if (code == null) {
            return null;
        }

        for (IndexCodeEnums indexCode : values()) {
            if (code.equals(indexCode.getCode())) {
                return indexCode.getType();
            }
        }
        return null;
    }

    /**
     * 通过code获取枚举对象
     *
     * @param code 指标代码
     * @return 枚举对象，如果未找到返回null
     */
    public static IndexCodeEnums getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (IndexCodeEnums indexCode : values()) {
            if (code.equals(indexCode.getCode())) {
                return indexCode;
            }
        }
        return null;
    }
}

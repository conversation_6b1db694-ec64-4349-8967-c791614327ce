package com.chinaservices.wms.sse.service;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.chinaservices.wms.sse.common.MessageTypeEnum;
import com.chinaservices.wms.sse.model.SseConnection;
import com.chinaservices.wms.sse.model.SseMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * SSE 消息服务
 */
@Slf4j
public class SseMessageService {

    private final SseConnectionManager connectionManager;

    public SseMessageService(SseConnectionManager connectionManager) {
        this.connectionManager = connectionManager;
    }

    /**
     * 发送消息给指定用户
     */
    public void sendMessageToUser(Long userId, String data, MessageTypeEnum messageTypeEnum, String businessType) {
        SseMessage sseMessage = SseMessage.builder().messageId(IdUtil.fastSimpleUUID()).businessType(businessType).data(data).userIds(List.of(userId)).messageType(messageTypeEnum).createTime(LocalDateTime.now()).build();
        sendMessage(sseMessage);
    }

    /**
     * 发送消息给多个用户
     */
    public void sendMessageToUsers(List<Long> userIds, String data, MessageTypeEnum messageTypeEnum, String businessType) {
        SseMessage sseMessage = SseMessage.builder().messageId(IdUtil.fastSimpleUUID()).businessType(businessType).data(data).userIds(userIds).messageType(messageTypeEnum).createTime(LocalDateTime.now()).build();
        sendMessage(sseMessage);
    }

    /**
     * 广播消息给所有连接
     */
    public void broadcastMessage(String data, MessageTypeEnum messageTypeEnum, String businessType) {
        SseMessage sseMessage = SseMessage.builder().messageId(IdUtil.fastSimpleUUID()).businessType(businessType).data(data).createTime(LocalDateTime.now()).messageType(messageTypeEnum).build();
        broadcastMessage(sseMessage);
    }

    /**
     * 发送SSE消息
     */
    public void sendMessage(SseMessage sseMessage) {
        if (sseMessage.getUserIds() == null || sseMessage.getUserIds().isEmpty()) {
            log.warn("消息目标用户列表为空，消息ID: {}", sseMessage.getMessageId());
            return;
        }
        // 异步发送消息
        CompletableFuture.runAsync(() -> {
            for (Long userId : sseMessage.getUserIds()) {
                sendMessageToUserConnections(userId, sseMessage);
            }
        }).exceptionally(throwable -> {
            log.error("发送SSE消息异常，消息ID: {}, 错误: {}", sseMessage.getMessageId(), throwable.getMessage(), throwable);
            return null;
        });
    }

    /**
     * 广播SSE消息
     */
    public void broadcastMessage(SseMessage sseMessage) {
        // 异步广播消息
        CompletableFuture.runAsync(() -> {
            List<SseConnection> connections = connectionManager.getAllActiveConnections();
            for (SseConnection connection : connections) {
                sendMessageToConnection(connection, sseMessage);
            }
        });
    }

    /**
     * 发送心跳消息
     */
    public void sendHeartbeat() {
        SseMessage heartbeatMessage = SseMessage.builder().messageId(IdUtil.fastSimpleUUID()).messageType(MessageTypeEnum.HEARTBEAT).data("ping").createTime(LocalDateTime.now()).build();
        broadcastMessage(heartbeatMessage);
    }

    /**
     * 发送消息到用户的所有连接
     */
    private void sendMessageToUserConnections(Long userId, SseMessage sseMessage) {
        List<SseConnection> connections = connectionManager.getUserConnections(userId);
        if (connections.isEmpty()) {
            log.debug("用户无活跃连接，用户ID: {}, 消息ID: {}", userId, sseMessage.getMessageId());
            return;
        }
        for (SseConnection connection : connections) {
            sendMessageToConnection(connection, sseMessage);
        }
    }

    /**
     * 发送消息到指定连接
     */
    private void sendMessageToConnection(SseConnection connection, SseMessage sseMessage) {
        try {
            SseEmitter emitter = connection.getEmitter();
            if (emitter == null) {
                log.warn("连接的发射器为空，连接ID: {}", connection.getConnectionId());
                connectionManager.removeConnection(connection.getConnectionId());
                return;
            }
            // 发送消息
            emitter.send(SseEmitter.event().id(sseMessage.getMessageId()).name(sseMessage.getMessageType().getCode()).data(JSONUtil.toJsonStr(sseMessage)));
            // 更新连接活跃时间
            connectionManager.updateLastActiveTime(connection.getConnectionId());
            log.debug("SSE消息发送成功，连接ID: {}, 用户ID: {}, 消息ID: {}", connection.getConnectionId(), connection.getUserId(), sseMessage.getMessageId());
        } catch (IOException e) {
            String errorMsg = e.getMessage();
            log.warn("发送SSE消息失败，连接ID: {}, 用户ID: {}, 消息ID: {}, 错误: {}", connection.getConnectionId(), connection.getUserId(), sseMessage.getMessageId(), errorMsg);
            connectionManager.removeConnection(connection.getConnectionId());
        } catch (Exception e) {
            log.error("发送SSE消息异常，连接ID: {}, 用户ID: {}, 消息ID: {}, 错误: {}", connection.getConnectionId(), connection.getUserId(), sseMessage.getMessageId(), e.getMessage(), e);
        }
    }
}

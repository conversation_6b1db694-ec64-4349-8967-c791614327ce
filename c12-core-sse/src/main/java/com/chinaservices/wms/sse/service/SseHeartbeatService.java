package com.chinaservices.wms.sse.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;

import static com.chinaservices.wms.sse.common.SseConstants.HEARTBEAT_INTERVAL;

/**
 * SSE 心跳服务
 * 定期发送心跳消息保持连接活跃，并清理过期连接
 */
@Slf4j
public class SseHeartbeatService {

    private final SseMessageService messageService;
    private final SseConnectionManager connectionManager;

    public SseHeartbeatService(SseMessageService messageService, SseConnectionManager connectionManager) {
        this.messageService = messageService;
        this.connectionManager = connectionManager;
    }

    /**
     * 每30秒发送一次心跳
     */
    @Scheduled(fixedRate = HEARTBEAT_INTERVAL)
    public void sendHeartbeat() {
        try {
            int activeConnections = connectionManager.getAllActiveConnections().size();
            if (activeConnections > 0) {
                messageService.sendHeartbeat();
            }
        } catch (Exception e) {
            log.error("发送SSE心跳消息失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 每2分钟清理一次过期连接和检查连接健康状态
     */
    @Scheduled(fixedRate = 120000)
    public void cleanExpiredConnections() {
        try {
            // 清理过期连接
            connectionManager.cleanExpiredConnections();

            // 检查不健康的连接
            var unhealthyConnections = connectionManager.getUnhealthyConnections();
            if (!unhealthyConnections.isEmpty()) {
                log.warn("发现 {} 个不健康的连接", unhealthyConnections.size());
                for (var conn : unhealthyConnections) {
                    log.warn("不健康连接详情 - 连接ID: {}, 用户ID: {}, 最后活跃: {}",
                        conn.getConnectionId(), conn.getUserId(), conn.getLastActiveTime());
                }
            }

            log.debug("SSE连接健康检查完成，过期连接清理完成");
        } catch (Exception e) {
            log.error("清理SSE过期连接或健康检查失败: {}", e.getMessage(), e);
        }
    }
}
package com.chinaservices.wms.sse.listener;

import com.chinaservices.wms.sse.service.SseDistributedService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.listener.MessageListener;
import org.springframework.stereotype.Component;

/**
 * SSE Redis 消息监听器
 * 使用 RedissonClient 监听 Redis 发布/订阅频道，处理分布式消息
 */
@Slf4j
public class SseRedisMessageListener implements MessageListener<String> {

    private final SseDistributedService distributedService;

    public SseRedisMessageListener(SseDistributedService distributedService) {
        this.distributedService = distributedService;
    }

    @Override
    public void onMessage(CharSequence channel, String message) {
        try {
            distributedService.handleDistributedMessage(message);
        } catch (Exception e) {
            log.error("处理Redis消息失败: {}", e.getMessage(), e);
        }
    }
}

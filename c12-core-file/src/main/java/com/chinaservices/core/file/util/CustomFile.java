package com.chinaservices.core.file.util;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ZipUtil;
import cn.hutool.json.JSONUtil;
import com.chinaservices.core.file.enums.DownLoadTemplateEnum;
import com.chinaservices.core.file.properties.TemplateFileProperties;
import com.chinaservices.excel.core.ExcelResult;
import com.chinaservices.excel.util.ExcelUtil;
import com.chinaservices.file.FileObject;
import com.chinaservices.file.FileStorage;
import com.chinaservices.sdk.exception.BusinessException;
import com.chinaservices.util.EnumUtil;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.WriterException;
import com.google.zxing.client.j2se.MatrixToImageConfig;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.tomcat.util.http.fileupload.IOUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.multipart.MultipartFile;

import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * 文件工具类
 *
 * <AUTHOR>
 * @date 2025/02/20
 **/
@Component
@Slf4j
public class CustomFile {

    private static final Integer MAX_FILE_SIZE = 20 * 1000 * 1000;

    private final FileStorage fileStorage;

    private final TemplateFileProperties templateFileProperties;

    private final ExecutorService executorService;

    private static final int CHUNK_SIZE = 5 * 1024 * 1024;

    private static final String TEMP_DIR = System.getProperty("java.io.tmpdir") + "/uploads/";

    public CustomFile(FileStorage fileStorage, TemplateFileProperties templateFileProperties) {
        this.fileStorage = fileStorage;
        this.templateFileProperties = templateFileProperties;
        this.executorService = ThreadUtil.newExecutor();
    }

    /**
     * 上传文件
     *
     * @param file
     * @return FileObject
     */
    public FileObject upload(MultipartFile file) {
        if (file.isEmpty()) {
            throw new BusinessException("文件为空");
        }
        if (file.getSize() > MAX_FILE_SIZE) {
            throw new BusinessException("单个文件超过限定的20M大小");
        }
        if (file.getSize() <= CHUNK_SIZE) {
            return this.uploadSingleFile(file);
        }
        return this.uploadInChunks(file);
    }

    /**
     * 单文件上传（小文件）
     */
    private FileObject uploadSingleFile(MultipartFile file) {
        FileObject fileObject = null;
        try {
            fileObject = fileStorage.putObject(file.getOriginalFilename(), file.getInputStream());
            fileObject.setUrl(fileStorage.getObjectSignUrl(fileObject.getUuid()));
            return fileObject;
        } catch (IOException e) {
            log.error(e.getMessage());
        }
        return fileObject;
    }

    /**
     * 分片上传（大文件）
     */
    private FileObject uploadInChunks(MultipartFile file) {
        String tempFilePrefix = UUID.randomUUID().toString();
        File tempDir = new File(TEMP_DIR);
        if (!tempDir.exists()) tempDir.mkdirs();
        List<Future<File>> futures = new ArrayList<>();
        try (InputStream originalStream = file.getInputStream()) {
            // 1. 创建分片临时文件
            byte[] buffer = new byte[CHUNK_SIZE];
            int chunkIndex = 0;
            int bytesRead;
            while ((bytesRead = originalStream.read(buffer)) != -1) {
                File tempFile = new File(TEMP_DIR + tempFilePrefix + "_" + chunkIndex++ + ".tmp");
                int finalBytesRead = bytesRead;
                futures.add(executorService.submit(() -> {
                    try (FileOutputStream fos = new FileOutputStream(tempFile)) {
                        fos.write(buffer, 0, finalBytesRead);
                    }
                    return tempFile;
                }));
            }
            // 2. 等待所有分片完成
            List<File> chunkFiles = new ArrayList<>();
            for (Future<File> future : futures) {
                chunkFiles.add(future.get());
            }
            // 3. 合并临时文件
            File mergedFile = new File(TEMP_DIR + tempFilePrefix + "_merged.tmp");
            try (FileOutputStream fos = new FileOutputStream(mergedFile)) {
                for (File chunkFile : chunkFiles) {
                    Files.copy(chunkFile.toPath(), fos);
                    chunkFile.delete();
                }
            }
            // 4. 上传最终文件
            try (FileInputStream fis = new FileInputStream(mergedFile)) {
                FileObject result = fileStorage.putObject(file.getOriginalFilename(), fis);
                result.setUrl(fileStorage.getObjectSignUrl(result.getUuid()));
                return result;
            } finally {
                mergedFile.delete();
            }
        } catch (IOException | InterruptedException | ExecutionException e) {
            throw new BusinessException("分片上传失败", e);
        }
    }

    /**
     * 上传文件
     *
     * @param file
     * @return FileObject
     */
    public FileObject upload(MultipartFile file, List<String> allowTypeList) {
        this.checkAllowType(file.getOriginalFilename(), allowTypeList);
        return this.upload(file);
    }

    /**
     * 校验文件格式
     *
     * @param originalFilename
     * @param allowTypeList
     */
    public void checkAllowType(String originalFilename, List<String> allowTypeList) {
        if (!CollectionUtil.contains(allowTypeList, StrUtil.subAfter(originalFilename, StrUtil.DOT, Boolean.TRUE).toLowerCase())) {
            throw new BusinessException("不支持的文件格式");
        }
    }

    /**
     * 上传文件
     *
     * @param originalFilename
     * @param inputStream
     * @return FileObject
     */
    public FileObject upload(String originalFilename, InputStream inputStream) {
        return fileStorage.putObject(originalFilename, inputStream, false);
    }

    /**
     * 删除文件
     *
     * @param fileId
     * @return
     */
    public String delete(String fileId) {
        fileStorage.deleteObject(fileId);
        return fileId;
    }

    /**
     * 获取文件
     *
     * @param fileId
     * @return
     */
    public FileObject getObject(String fileId) {
        return fileStorage.getObject(fileId);
    }

    /**
     * 获取文件的URL地址
     *
     * @param fileId
     * @return
     */
    public String getObjectSignUrl(String fileId) {
        return this.getObjectSignUrl(fileId, null);
    }

    /**
     * 获取文件的URL地址
     *
     * @param fileId
     * @return
     */
    public String getObjectSignUrl(String fileId, Integer timeout) {
        return ObjUtil.isNull(timeout) ? fileStorage.getObjectSignUrl(fileId) : fileStorage.getObjectSignUrl(fileId, timeout);
    }

    /**
     * 获取上传文件的URL地址
     *
     * @param fileId
     * @return
     */
    public String getUploadObjectSignUrl(String fileId) {
        return this.getUploadObjectSignUrl(fileId, null);
    }

    /**
     * 获取上传文件的URL地址
     *
     * @param fileId
     * @return
     */
    public String getUploadObjectSignUrl(String fileId, Integer timeout) {
        return ObjUtil.isNull(timeout) ? fileStorage.getObjectSignUrl(fileId) : fileStorage.getObjectSignUrl(fileId, timeout);
    }

    /**
     * 获取文件的URL地址集合
     *
     * @param fileIdList
     * @return
     */
    public List<String> getObjectSignUrlList(List<String> fileIdList) {
        return this.getObjectSignUrlList(fileIdList, null);
    }

    /**
     * 获取文件的URL地址集合
     *
     * @param fileIdList
     * @return
     */
    public List<String> getObjectSignUrlList(List<String> fileIdList, Integer timeout) {
        return ObjUtil.isNull(timeout) ? fileIdList.stream().map(fileStorage::getObjectSignUrl).collect(Collectors.toList()) : fileIdList.stream().map(fileId -> fileStorage.getObjectSignUrl(fileId, timeout)).collect(Collectors.toList());
    }

    /**
     * 获取上传文件的URL地址集合
     *
     * @param fileIdList
     * @return
     */
    public List<String> getUploadObjectSignUrlList(List<String> fileIdList) {
        return this.getUploadObjectSignUrlList(fileIdList, null);
    }

    /**
     * 获取上传文件的URL地址集合
     *
     * @param fileIdList
     * @return
     */
    public List<String> getUploadObjectSignUrlList(List<String> fileIdList, Integer timeout) {
        return ObjUtil.isNull(timeout) ? fileIdList.stream().map(fileStorage::getUploadObjectSignUrl).collect(Collectors.toList()) : fileIdList.stream().map(fileId -> fileStorage.getUploadObjectSignUrl(fileId, timeout)).collect(Collectors.toList());
    }

    /**
     * 下载文件
     *
     * @param id
     * @param response
     */
    public void download(@PathVariable(value = "id") String id, HttpServletResponse response) {
        FileObject fileObject = fileStorage.getObject(id);
        ServletOutputStream servletOutputStream = null;
        InputStream inputStream = null;
        try {
            servletOutputStream = response.getOutputStream();
            response.reset();
            response.setContentType(fileObject.getContentType());
            response.setHeader("Content-type", fileObject.getContentType());
            response.setCharacterEncoding("UTF-8");
            String fileName = URLEncoder.encode(fileObject.getOrigFileName(), "UTF-8");
            response.setHeader("Content-disposition", "attachment; filename=" + fileName);
            inputStream = fileObject.getInputStream();
            IoUtil.copy(inputStream, servletOutputStream);
            response.flushBuffer();
        } catch (Exception e) {
            throw new RuntimeException("下载文件异常", e);
        } finally {
            IoUtil.close(servletOutputStream);
            IoUtil.close(inputStream);
        }
    }

    /**
     * 预览文件
     *
     * @param fileId
     * @param response
     */
    public void preview(String fileId, HttpServletResponse response) {
        FileObject fileObject = fileStorage.getObject(fileId);
        response.setContentType(fileObject.getContentType());
        InputStream input = fileObject.getInputStream();
        OutputStream out = null;
        try {
            out = response.getOutputStream();
            byte[] by = new byte[1024];
            int i = 0;
            while ((i = input.read(by)) != -1) {
                out.write(by, 0, i);
            }
        } catch (Exception e) {
            throw new RuntimeException("预览文件异常", e);
        } finally {
            IOUtils.closeQuietly(out);
            IOUtils.closeQuietly(input);
        }
    }

    /**
     * 模板下载
     *
     * @param type
     * @return
     */
    public FileObject downLoadTemplate(String type) {
        Map<String, String> downLoadTemplateMap = templateFileProperties.getDownLoadTemplate();
        if (MapUtil.isEmpty(downLoadTemplateMap)) {
            throw new RuntimeException(StrUtil.format("模板下载配置不存在：{}", JSONUtil.toJsonStr(downLoadTemplateMap)));
        }
        DownLoadTemplateEnum downLoadTemplate = EnumUtil.getBy(DownLoadTemplateEnum::getType, type);
        if (ObjUtil.isNull(downLoadTemplate)) {
            throw new RuntimeException(StrUtil.format("模板下载type不存在：{}", JSONUtil.toJsonStr(downLoadTemplate)));
        }
        String fileName = downLoadTemplate.getValue();
        String fileId = MapUtil.getStr(downLoadTemplateMap, downLoadTemplate.getCode());
        if (StrUtil.isEmpty(fileId)) {
            throw new RuntimeException(StrUtil.format("模板下载key不存在：{}", fileId));
        }
        FileObject fileObject = this.getObject(fileId);
        FileObject fileBaseObject = new FileObject();
        fileBaseObject.setOrigFileName(StrUtil.isEmpty(fileName) ? fileObject.getOrigFileName() : fileName);
        fileBaseObject.setUuid(fileObject.getUuid());
        fileBaseObject.setUrl(getObjectSignUrl(fileObject.getUuid()));
        return fileBaseObject;
    }

    /**
     * 模板下载
     *
     * @param type
     * @return
     */
    public void downLoadTemplate(String type, HttpServletResponse response) {
        Map<String, String> downLoadTemplateMap = templateFileProperties.getDownLoadTemplate();
        if (MapUtil.isEmpty(downLoadTemplateMap)) {
            throw new RuntimeException(StrUtil.format("模板下载配置不存在：{}", JSONUtil.toJsonStr(downLoadTemplateMap)));
        }
        DownLoadTemplateEnum downLoadTemplate = EnumUtil.getBy(DownLoadTemplateEnum::getType, type);
        if (ObjUtil.isNull(downLoadTemplate)) {
            throw new RuntimeException(StrUtil.format("模板下载type不存在：{}", JSONUtil.toJsonStr(downLoadTemplate)));
        }
        String fileId = MapUtil.getStr(downLoadTemplateMap, downLoadTemplate.getCode());
        if (StrUtil.isEmpty(fileId)) {
            throw new RuntimeException(StrUtil.format("模板下载key不存在：{}", fileId));
        }
        this.download(fileId, response);
    }

    /**
     * 批量下载
     *
     * @param fileIdList
     * @param zipName
     * @return
     */
    public FileObject batchDownload(List<String> fileIdList, String zipName) {
        return this.batchDownload(fileIdList, zipName, null);
    }

    /**
     * 批量下载到文件夹，下载完成后打成 zip 包上传
     *
     * @param fileIdList 文件ID列表
     * @param zipName    zip文件名
     * @param timeout    签名URL超时时间
     * @return 上传后的文件对象
     */
    public FileObject batchDownload(List<String> fileIdList, String zipName, Integer timeout) {
        // 创建临时文件夹
        File tempDir = FileUtil.mkdir(FileUtil.getTmpDirPath() + File.separator + "batch-download-" + System.currentTimeMillis());
        File tempZipFile = null;
        try {
            // 使用 CountDownLatch 来等待所有下载任务完成
            CountDownLatch latch = new CountDownLatch(fileIdList.size());
            // 多线程下载文件到临时文件夹
            for (String fileId : fileIdList) {
                executorService.submit(() -> {
                    try {
                        FileObject fileObject = fileStorage.getObject(fileId);
                        File destFile = new File(tempDir, fileObject.getOrigFileName());
                        try (InputStream fileInputStream = fileObject.getInputStream()) {
                            FileUtil.writeFromStream(fileInputStream, destFile);
                        }
                    } catch (Exception e) {
                        throw new RuntimeException("Failed to download file: " + fileId, e);
                    } finally {
                        latch.countDown();
                    }
                });
            }
            // 等待所有下载任务完成
            if (!latch.await(60, TimeUnit.SECONDS)) {
                throw new RuntimeException("Timeout while waiting for files to download");
            }
            // 将文件夹打包成 zip 文件
            tempZipFile = FileUtil.createTempFile("batch-download-", ".zip", false); // 在这里初始化 tempZipFile
            ZipUtil.zip(tempDir.getAbsolutePath(), tempZipFile.getAbsolutePath(), true);
            // 上传 zip 文件
            try (FileInputStream fis = new FileInputStream(tempZipFile)) {
                FileObject fileObject = fileStorage.putObject(zipName, fis);
                FileObject fileBaseObject = new FileObject();
                fileBaseObject.setOrigFileName(fileObject.getOrigFileName());
                fileBaseObject.setUuid(fileObject.getUuid());
                fileBaseObject.setUrl(ObjUtil.isNull(timeout) ? fileStorage.getObjectSignUrl(fileObject.getUuid()) : fileStorage.getObjectSignUrl(fileObject.getUuid(), timeout));
                return fileBaseObject;
            }
        } catch (Exception e) {
            throw new RuntimeException("Failed to batch download and zip files", e);
        } finally {
            // 删除临时文件夹和临时 zip 文件
            FileUtil.del(tempDir);
            if (tempZipFile != null) {
                FileUtil.del(tempZipFile);
            }
        }
    }

    /**
     * 导入文件
     *
     * @param file
     * @return
     */
    public <T> List<T> importExcel(MultipartFile file, Class<T> clazz) {
        InputStream inputStream = null;
        try {
            inputStream = file.getInputStream();
            ExcelResult<T> excelResult = ExcelUtil.importExcel(inputStream, clazz, true);
            return excelResult.getList();
        } catch (Exception e) {
            log.error("导入文件异常：{}", e.getMessage());
            throw new RuntimeException(e);
        }
    }

    /**
     * 条形码生成
     *
     * @param code
     * @return
     * @throws WriterException
     * @throws IOException
     */
    public FileObject barcodeGenerate(String code) throws WriterException, IOException {
        // 设置编码相关的提示信息
        Map<EncodeHintType, Object> hints = MapUtil.newHashMap();
        hints.put(EncodeHintType.CHARACTER_SET, StandardCharsets.UTF_8);
        hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.L);
        // 使用多格式编码器生成条形码的比特矩阵
        MultiFormatWriter writer = new MultiFormatWriter();
        BitMatrix bitMatrix = writer.encode(code, BarcodeFormat.CODE_128, 300, 100, hints);
        // 定义背景色和前景色
        int foregroundColor = Color.BLACK.getRGB();
        int backgroundColor = Color.WHITE.getRGB();
        // 将比特矩阵转换为 BufferedImage 对象，指定背景色和前景色
        MatrixToImageConfig matrixToImageConfig = new MatrixToImageConfig(foregroundColor, backgroundColor);
        BufferedImage barcodeImage = MatrixToImageWriter.toBufferedImage(bitMatrix, matrixToImageConfig);
        // 定义文本区域的高度
        int textHeight = 20;
        // 定义条形码和文字之间的间距
        int spacing = 5;
        // 创建一个新的 BufferedImage 对象，高度增加以容纳下方的文本
        BufferedImage combinedImage = new BufferedImage(barcodeImage.getWidth(), barcodeImage.getHeight() + textHeight + spacing, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = combinedImage.createGraphics();
        // 设置背景为白色
        g2d.setColor(Color.WHITE);
        g2d.fillRect(0, 0, combinedImage.getWidth(), combinedImage.getHeight());
        // 绘制条形码图像
        g2d.drawImage(barcodeImage, 0, 0, null);
        // 设置字体和颜色
        g2d.setFont(new Font("Arial", Font.PLAIN, 16));
        g2d.setColor(Color.BLACK);
        // 计算文本的绘制位置，使其居中显示
        int textX = (combinedImage.getWidth() - g2d.getFontMetrics().stringWidth(code)) / 2;
        int textY = barcodeImage.getHeight() + textHeight - 5;
        // 绘制文本
        g2d.drawString(code, textX, textY);
        g2d.dispose();
        // 将BufferedImage转换为输入流
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        javax.imageio.ImageIO.write(combinedImage, "png", outputStream);
        ByteArrayInputStream inputStream = new ByteArrayInputStream(outputStream.toByteArray());
        // 将条形码上传到对象存储
        return fileStorage.putObject(code + StrUtil.DASHED + System.currentTimeMillis() + ".png", inputStream, false);
    }
}

package com.chinaservices.core.file.config;

import com.chinaservices.core.config.properties.ExportConfigProperties;
import com.chinaservices.core.controller.TaskController;
import com.chinaservices.core.dao.TaskDao;
import com.chinaservices.core.schedule.DelExpireTaskFileSchedule;
import com.chinaservices.core.schedule.TaskScanSchedule;
import com.chinaservices.core.service.TaskProcessService;
import com.chinaservices.core.service.TaskScheduleService;
import com.chinaservices.core.service.TaskService;
import com.chinaservices.core.service.execute.ParallelTaskExecuteService;
import com.chinaservices.core.service.execute.SerialTaskExecuteService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

@Configuration
public class ExportConfig {

    @Bean
    public ExportConfigProperties exportConfigProperties() {
        return new ExportConfigProperties();
    }

    @Bean
    public TaskController taskController() {
        return new TaskController();
    }

    @Bean
    public TaskService taskService() {
        return new TaskService();
    }

    @Bean
    public TaskDao taskDao() {
        return new TaskDao();
    }

    @Bean
    public DelExpireTaskFileSchedule delExpireTaskFileSchedule() {
        return new DelExpireTaskFileSchedule();
    }

   // @Bean
    public TaskScanSchedule taskScanSchedule() {
        return new TaskScanSchedule();
    }

    @Bean
    public ParallelTaskExecuteService parallelTaskExecuteService() {
        return new ParallelTaskExecuteService();
    }

    @Bean
    public SerialTaskExecuteService serialTaskExecuteService() {
        return new SerialTaskExecuteService();
    }

    @Bean
    @Primary
    public TaskProcessService taskProcessService() {
        return new TaskProcessService();
    }

    @Bean
    public TaskScheduleService taskScheduleService() {
        return new TaskScheduleService();
    }
}
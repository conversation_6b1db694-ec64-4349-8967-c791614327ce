package com.chinaservices.core.service;

import com.chinaservices.core.config.properties.ExportConfigProperties;
import com.chinaservices.core.constant.TaskStatusEnum;
import com.chinaservices.core.constant.TaskTypeEnum;
import com.chinaservices.core.dao.TaskDao;
import com.chinaservices.core.domain.TaskProcessSaveItem;
import com.chinaservices.core.domain.TaskStatueEventItem;
import com.chinaservices.core.event.TaskStatusEvent;
import com.chinaservices.core.model.Task;
import com.chinaservices.core.service.execute.BaseTaskExecuteService;
import com.chinaservices.core.service.execute.ParallelTaskExecuteService;
import com.chinaservices.core.service.execute.SerialTaskExecuteService;
import com.chinaservices.locker.LockInfo;
import com.chinaservices.locker.LockTemplate;
import com.chinaservices.session.SessionContext;
import com.chinaservices.session.SessionUserInfo;
import com.chinaservices.util.StringUtil;
import org.apache.commons.io.FileUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;

/**
 * <AUTHOR>
 **/
@Service
public class TaskProcessService {

    private static final Logger log = LogManager.getLogger(TaskProcessService.class);
    @Autowired
    private TaskDao taskDao;
    @Autowired
    private ExportConfigProperties exportConfigProperties;
    @Autowired
    private ApplicationEventPublisher eventPublisher;
    @Autowired
    private SerialTaskExecuteService serialTaskExecuteService;
    @Autowired
    private ParallelTaskExecuteService parallelTaskExecuteService;
    @Autowired
    private LockTemplate lockTemplate;

    public TaskProcessService() {
    }

    @Transactional(
            propagation = Propagation.NOT_SUPPORTED
    )
    public void process(Task task, LockInfo lockInfo) {
        if (task.getTaskType().equals(TaskTypeEnum.Serial.getType())) {
            this.process(this.serialTaskExecuteService, task, lockInfo);
        } else {
            SessionUserInfo sessionUserInfo = SessionContext.get();
            (new Thread(() -> {
                SessionContext.put(sessionUserInfo);
                this.process(this.parallelTaskExecuteService, task, lockInfo);
            })).start();
        }

    }

    public void process(BaseTaskExecuteService taskProcessService, Task task, LockInfo lockInfo) {
        if (StringUtils.hasLength(task.getLanguage())) {
            LocaleContextHolder.setLocale(Locale.forLanguageTag(task.getLanguage()));
        } else {
            LocaleContextHolder.setLocale(Locale.SIMPLIFIED_CHINESE);
        }

        log.info("任务开始执行:{}", task.getId());
        TaskProcessSaveItem processSaveItem = new TaskProcessSaveItem();
        BeanUtils.copyProperties(task, processSaveItem);
        processSaveItem.setTaskStatus(TaskStatusEnum.PROCESSING.getCode());
        processSaveItem.setStartTime(new Date());
        this.taskDao.saveOrUpdate(processSaveItem);
        if (lockInfo != null) {
            this.lockTemplate.releaseLock(lockInfo);
        }

        this.sendTaskStatusEvent(processSaveItem.getId(), processSaveItem.getTaskStatus(), (Throwable)null);
        Path baseDir = null;

        try {
            baseDir = Files.createTempDirectory("c12-export");
            String fileUuid = taskProcessService.runTask(task, baseDir);
            processSaveItem.setFileUuid(fileUuid);
            processSaveItem.setTaskStatus(TaskStatusEnum.COMPLETED.getCode());
            processSaveItem.setErrorMsg((String)null);
            if (task.getExpiredTime() == null) {
                Calendar calendar = Calendar.getInstance();
                calendar.add(5, (int)this.exportConfigProperties.getTaskFileExpire().toDays());
                processSaveItem.setExpiredTime(calendar.getTime());
            }

            log.info("任务执行成功:{}", task.getId());
            this.sendTaskStatusEvent(processSaveItem.getId(), processSaveItem.getTaskStatus(), (Throwable)null);
        } catch (Throwable throwable) {
            log.error("任务执行失败", throwable);
            processSaveItem.setTaskStatus(TaskStatusEnum.FAILED.getCode());
            processSaveItem.setErrorMsg(StringUtil.substring(throwable.toString(), 0, 250));
            this.sendTaskStatusEvent(processSaveItem.getId(), processSaveItem.getTaskStatus(), throwable);
        } finally {
            if (baseDir != null) {
                try {
                    FileUtils.deleteDirectory(baseDir.toFile());
                } catch (IOException e) {
                    log.error("临时文件夹删除失败，{}", baseDir.toString(), e);
                }
            }

        }

        processSaveItem.setFinishTime(new Date());
        this.taskDao.saveOrUpdate(processSaveItem);
    }

    private void sendTaskStatusEvent(Long taskId, Integer status, Throwable throwable) {
        TaskStatueEventItem taskStatueEventItem = new TaskStatueEventItem();
        taskStatueEventItem.setId(taskId);
        taskStatueEventItem.setTaskStatus(status);
        taskStatueEventItem.setThrowable(throwable);
        this.eventPublisher.publishEvent(new TaskStatusEvent(taskStatueEventItem));
    }
}

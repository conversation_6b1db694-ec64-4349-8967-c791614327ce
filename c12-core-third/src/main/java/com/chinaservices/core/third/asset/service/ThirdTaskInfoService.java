package com.chinaservices.core.third.asset.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.http.*;
import cn.hutool.json.JSONUtil;
import com.chinaservices.core.third.annotation.EdiLogAnnotation;
import com.chinaservices.core.third.asset.domain.*;
import com.chinaservices.core.third.asset.properties.AssetProperties;
import com.chinaservices.core.third.constant.ThirdResponse;
import com.chinaservices.core.third.enums.ResponseCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
public class ThirdTaskInfoService {

    private final AssetProperties assetProperties;

    public ThirdTaskInfoService(AssetProperties assetProperties) {
        this.assetProperties = assetProperties;
    }

    public static final int TASK_INFO_TIME_OUT = 15000;

    /**
     * 启动盘存任务
     *
     * @param assetStartRequest 启动盘存任务请求参数
     * @return ThirdResponse
     */
    @EdiLogAnnotation(apiDescription = "启动盘存任务", methodType = "POST")
    public ThirdResponse ableTaskInfo(AssetStartRequest assetStartRequest) {
        Map<String, Object> paramMap = BeanUtil.beanToMap(assetStartRequest, Boolean.FALSE, Boolean.TRUE);
        String formData = HttpUtil.toParams(paramMap);
        log.info("启动盘存任务，请求地址：{}，request：{}", assetProperties.getAbleTaskInfoUrl(), formData);
        HttpResponse httpResponse = null;
        try {
            httpResponse = HttpRequest.post(assetProperties.getAbleTaskInfoUrl())
                    .contentType(ContentType.FORM_URLENCODED.getValue())
                    .body(formData)
                    .timeout(TASK_INFO_TIME_OUT)
                    .execute();
            String responseBody = httpResponse.body();
            log.info("启动盘存任务，response：{}", responseBody);
            AssetCommonResponse response = JSONUtil.toBean(responseBody, AssetCommonResponse.class);
            if (ObjUtil.isNotNull(response) && ObjUtil.equals(HttpStatus.HTTP_OK, response.getCode())) {
                return ThirdResponse.success(response.getResult(), assetProperties.getAbleTaskInfoUrl());
            } else {
                return ThirdResponse.fail(String.valueOf(response.getCode()), JSONUtil.toJsonStr(response.getMsg()), assetProperties.getAbleTaskInfoUrl());
            }
        } catch (Exception e) {
            log.error("启动盘存任务调用失败", e);
            return ThirdResponse.exception(JSONUtil.toJsonStr(e.getMessage()), assetProperties.getAbleTaskInfoUrl());
        } finally {
            if (httpResponse != null) {
                httpResponse.close();
            }
        }
    }

    /**
     * 停止盘存任务
     *
     * @param assetStopRequest 停止盘存任务请求参数
     * @return ThirdResponse
     */
    @EdiLogAnnotation(apiDescription = "停止盘存任务", methodType = "POST")
    public ThirdResponse stopInout(AssetStopRequest assetStopRequest) {
        Map<String, Object> paramMap = BeanUtil.beanToMap(assetStopRequest, Boolean.FALSE, Boolean.TRUE);
        String formData = HttpUtil.toParams(paramMap);
        log.info("停止盘存任务，请求地址：{}，request：{}", assetProperties.getStopTaskInfoUrl(), formData);
        HttpResponse httpResponse = null;
        try {
            httpResponse = HttpRequest.post(assetProperties.getStopTaskInfoUrl())
                    .contentType(ContentType.FORM_URLENCODED.getValue())
                    .body(formData)
                    .timeout(TASK_INFO_TIME_OUT)
                    .execute();
            String responseBody = httpResponse.body();
            log.info("停止盘存任务，response：{}", responseBody);
            AssetCommonResponse response = JSONUtil.toBean(responseBody, AssetCommonResponse.class);
            if (ObjUtil.isNotNull(response) && ObjUtil.equals(HttpStatus.HTTP_OK, response.getCode())) {
                return ThirdResponse.success(response.getResult(), assetProperties.getStopTaskInfoUrl());
            } else {
                return ThirdResponse.fail(String.valueOf(response.getCode()), JSONUtil.toJsonStr(response.getMsg()), assetProperties.getStopTaskInfoUrl());
            }
        } catch (Exception e) {
            log.error("停止盘存任务调用失败", e);
            return ThirdResponse.exception(JSONUtil.toJsonStr(e.getMessage()), assetProperties.getStopTaskInfoUrl());
        } finally {
            if (httpResponse != null) {
                httpResponse.close();
            }
        }
    }

    /**
     * 盘存任务和盘存结果
     *
     * @param porchQueryRequest 盘存任务和盘存结果请求
     * @return ThirdResponse
     */
    @EdiLogAnnotation(apiDescription = "盘存任务和盘存结果", methodType = "POST")
    public ThirdResponse queryInoutInfo(AssetQueryRequest porchQueryRequest) {
        Map<String, Object> paramMap = BeanUtil.beanToMap(porchQueryRequest, Boolean.FALSE, Boolean.TRUE);
        String formData = HttpUtil.toParams(paramMap);
        log.info("盘存任务和盘存结果，请求地址：{}，request：{}", assetProperties.getQueryTaskInfoUrl(), formData);
        HttpResponse httpResponse = null;
        try {
            httpResponse = HttpRequest.post(assetProperties.getQueryTaskInfoUrl())
                    .contentType(ContentType.FORM_URLENCODED.getValue())
                    .body(formData)
                    .timeout(TASK_INFO_TIME_OUT)
                    .execute();
            String responseBody = httpResponse.body();
            log.info("盘存任务和盘存结果，response：{}", responseBody);
            AssetCommonResponse response = JSONUtil.toBean(responseBody, AssetCommonResponse.class);
            if (ObjUtil.isNotNull(response) && ObjUtil.equals(HttpStatus.HTTP_OK, response.getCode())) {
                AssetResultResponse assetResultResponse = JSONUtil.toBean(response.getResult().toString(), AssetResultResponse.class);
                log.info("盘存任务和盘存结果，responseData：{}", JSONUtil.toJsonStr(assetResultResponse));
                return ThirdResponse.success(assetResultResponse, assetProperties.getQueryTaskInfoUrl());
            } else {
                return ThirdResponse.fail(String.valueOf(response.getCode()), JSONUtil.toJsonStr(response.getMsg()), assetProperties.getQueryTaskInfoUrl());
            }
        } catch (Exception e) {
            log.error("盘存任务和盘存结果请求异常:", e);
            return ThirdResponse.exception(ResponseCodeEnum.RESPONSE_DATA_NOT_EXISTS.getMessage(), assetProperties.getQueryTaskInfoUrl());
        } finally {
            if (httpResponse != null) {
                httpResponse.close();
            }
        }
    }

    /**
     * 查询盘存任务中最近 10 秒标签
     *
     * @param assetQueryLastRequest 查询盘存任务中最近 10 秒标签请求参数
     * @return ThirdResponse
     */
    @EdiLogAnnotation(apiDescription = "查询盘存任务中最近 10 秒标签", methodType = "POST")
    public ThirdResponse queryLastInout(AssetQueryLastRequest assetQueryLastRequest) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("current", assetQueryLastRequest.getCurrent());
        paramMap.put("limit", assetQueryLastRequest.getLimit());
        String formData = HttpUtil.toParams(paramMap);
        log.info("查询盘存任务中最近 10 秒标签，请求地址：{}，request：{}", assetProperties.getQueryLastTaskInfoUrl(), formData);
        HttpResponse httpResponse = null;
        try {
            httpResponse = HttpRequest.post(assetProperties.getQueryLastTaskInfoUrl())
                    .contentType(ContentType.FORM_URLENCODED.getValue())
                    .body(formData)
                    .timeout(TASK_INFO_TIME_OUT)
                    .execute();
            String responseBody = httpResponse.body();
            log.info("查询盘存任务中最近 10 秒标签，response：{}", responseBody);
            AssetCommonResponse response = JSONUtil.toBean(responseBody, AssetCommonResponse.class);
            if (ObjUtil.isNotNull(response) && ObjUtil.equals(HttpStatus.HTTP_OK, response.getCode())) {
                AssetPageResponse porchResultResponse = JSONUtil.toBean(response.getResult().toString(), AssetPageResponse.class);
                return ThirdResponse.success(porchResultResponse, assetProperties.getQueryLastTaskInfoUrl());
            } else {
                return ThirdResponse.fail(String.valueOf(response.getCode()), JSONUtil.toJsonStr(response.getMsg()), assetProperties.getQueryLastTaskInfoUrl());
            }
        } catch (Exception e) {
            log.error("查询盘存任务中最近 10 秒标签调用失败", e);
            return ThirdResponse.exception(JSONUtil.toJsonStr(e.getMessage()), assetProperties.getQueryLastTaskInfoUrl());
        } finally {
            if (httpResponse != null) {
                httpResponse.close();
            }
        }
    }
}

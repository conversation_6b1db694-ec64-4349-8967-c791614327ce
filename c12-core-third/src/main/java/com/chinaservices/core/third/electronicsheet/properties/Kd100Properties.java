package com.chinaservices.core.third.electronicsheet.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

@ConfigurationProperties(prefix = "kd100")
@Data
public class Kd100Properties {

    /**
     * 授权码，请到快递100页面申请企业版接口获取
     * 申请企业版链接：https://api.kuaidi100.com/register
     */
    private String key;

    /**
     * secret在企业管理后台获取
     * 企业管理后台链接：https://api.kuaidi100.com/login
     */
    private String secret;

    /**
     * 电子面单下单和取消地址
     */
    private String orderUrl;
}

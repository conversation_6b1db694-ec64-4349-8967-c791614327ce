package com.chinaservices.core.third.porch.domain;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * <AUTHOR>
 * . 启动进出库入参
 */
@Data
public class PorchStartRequest {

    /**
     * 任务名称
     */
    @NotBlank(message = "任务名称不能为空")
    private String taskname;

    /**
     * 设备位置
     */
    @NotBlank(message = "任务位置不能为空")
    private String location;

    /**
     * 必填，-1：进门；1：出门；0：不确定
     */
    @NotBlank(message = "盘存任务的类型不能为空")
    private String type;
}

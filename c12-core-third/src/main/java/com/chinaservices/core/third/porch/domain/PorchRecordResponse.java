package com.chinaservices.core.third.porch.domain;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 记录详情类
 * @date 2025/7/4 17:06
 **/
@Data
public class PorchRecordResponse {

    private int id;
    private int taskid;
    private String rid;
    private String qid;
    private int antennaid;
    private String epc;
    private int inventoryCounter;
    private String assetCode;
    private String assetName;
    private String assetType;
    private String position;
    private String datetime;
    private String inOutStatus;
}

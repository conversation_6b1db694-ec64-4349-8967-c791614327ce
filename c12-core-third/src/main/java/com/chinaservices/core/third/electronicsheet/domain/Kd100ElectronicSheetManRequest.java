package com.chinaservices.core.third.electronicsheet.domain;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Data
public class Kd100ElectronicSheetManRequest {

    /**
     * 收件人/寄件人姓名
     */
    @NotBlank(message = "收件人/寄件人姓名不能为空")
    private String name;

    /**
     * 收件人/寄件人的手机号，手机号和电话号二者其一必填
     */
    @NotBlank(message = "收件人/寄件人的手机号不能为空")
    private String mobile;

    /**
     * 收件人/寄件人的电话号，手机号和电话号二者其一必填
     */
    @NotBlank(message = "收件人/寄件人的电话号不能为空")
    private String tel;

    /**
     * 收件人/寄件人所在完整地址，如广东深圳市南山区科技南十二路金蝶软件园B10
     */
    @NotBlank(message = "收件人/寄件人所在完整地址不能为空")
    private String printAddr;

    /**
     * 收件人/寄件人所在公司名称
     */
    private String company;
}

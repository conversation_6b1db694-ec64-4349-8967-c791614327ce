package com.chinaservices.core.third.asset.domain;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class AssetTaskInfoResponse {

    private Long id;
    private String name;
    private String type;
    private String location;
    private Long timerId;
    private String timerName;
    private String status;
    private int assetNum;
    private int inventoryMode;
    private double inventoryRate;
    private int inventoryTagNum;
    private String incomingAssets;
    private String outgoingAssets;
    private String paras;
    private int stoped;
    private LocalDateTime createtime;
}

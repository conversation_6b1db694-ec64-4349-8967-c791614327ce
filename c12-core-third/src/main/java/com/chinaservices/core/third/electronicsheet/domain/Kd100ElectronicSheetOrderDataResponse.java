package com.chinaservices.core.third.electronicsheet.domain;

import lombok.Data;

@Data
public class Kd100ElectronicSheetOrderDataResponse {

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 快递单号
     */
    private String kuaidinum;

    /**
     * 子单号，多个子单时使用","隔开。比如JD6666666,JD888888,JD99999
     */
    private String childNum;

    /**
     * 回单号，部分快递公司回单会返回回单号
     */
    private String returnNum;

    /**
     * 面单短链，printType为IMAGE或者HTML时的面单短链 多个面单时使用","隔开。
     * 比如http://api.kuaidi100.com/label/1, http://api.kuaidi100.com/label/2, http://api.kuaidi100.com/label/3
     * 面单异步生成，请求还未生成时，可以稍后重试
     */
    private String label;

    /**
     * 大头笔，用于显示于电子面单上规定位置，非必需，是否有值取决于快递公司
     */
    private String bulkpen;

    /**
     * 始发地区域编码
     */
    private String orgCode;

    /**
     * 始发地/始发网点名称
     */
    private String orgName;

    /**
     * 目的地区域编码
     */
    private String destCode;

    /**
     * 目的地/到达网点
     */
    private String destName;

    /**
     * 始发分拣编码
     */
    private String orgSortingCode;

    /**
     * 始发分拣名称
     */
    private String orgSortingName;

    /**
     * 目的分栋编码
     */
    private String destSortingCode;

    /**
     * 目的分栋中心名称
     */
    private String destSortingName;

    /**
     * 始发其他信息
     */
    private String orgExtra;

    /**
     * 目的其他信息
     */
    private String destExtra;

    /**
     * 集包编码
     */
    private String pkgCode;

    /**
     * 集包地名称
     */
    private String pkgName;

    /**
     * 路区
     */
    private String road;

    /**
     * 二维码
     */
    private String qrCode;

    /**
     * 快递公司订单号
     */
    private String kdComOrderNum;

    /**
     * 快递业务类型编码
     */
    private String expressCode;

    /**
     * 快递业务类型名称
     */
    private String expressName;
}

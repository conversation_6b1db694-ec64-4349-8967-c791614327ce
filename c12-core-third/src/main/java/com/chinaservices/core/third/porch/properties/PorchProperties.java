package com.chinaservices.core.third.porch.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * <AUTHOR>
 */
@ConfigurationProperties(prefix = "porch")
@Data
public class PorchProperties {

    /**
     * 查询进出门详情
     */
    private String queryInoutUrl;

    /**
     * 启动进出库
     */
    private String ableInoutUrl;

    /**
     * 停止进出库
     */
    private String stopInoutUrl;

    /**
     * 查询最近一段时间的识别结果
     */
    private String queryLastInoutUrl;
}

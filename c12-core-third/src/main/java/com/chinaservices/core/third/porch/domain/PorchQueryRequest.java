package com.chinaservices.core.third.porch.domain;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * 查询进出门详情信息
 */
@Data
public class PorchQueryRequest {

    /**
     * 任务Id
     */
    @NotNull(message = "任务不能为空")
    private Integer taskId;

    /**
     * 进出门状态
     */
    @NotNull(message = "进出门状态不能为空")
    private Integer inoutStatus;

    /**
     * 当前页码
     */
    @NotNull(message = "当前页码不能为空")
    private Integer current;

    /**
     * 每页记录数
     */
    @NotNull(message = "每页记录数不能为空")
    private Integer limit;
}

package com.chinaservices.core.third.electronicsheet.domain;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Data
public class Kd100ElectronicSheetCancelRequest {

    /**
     * 电子面单客户账户或月结账号；电子面单下单时有填写，则电子面单取消时必填
     */
    private String partnerId;

    /**
     * 电子面单密码；电子面单下单时有填写，则电子面单取消时必填
     */
    private String partnerKey;

    /**
     * 电子面单密钥；电子面单下单时有填写，则电子面单取消时必填
     */
    private String partnerSecret;

    /**
     * 电子面单客户账户名称；电子面单下单时有填写，则电子面单取消时必填
     */
    private String partnerName;

    /**
     * 收件网点名称,由快递公司当地网点分配，
     * 若使用淘宝授权填入（taobao），
     * 使用菜鸟授权填入（cainiao),
     * 使用京东授权填入（jdalpha),
     * 使用拼多多授权填入(pinduoduoWx)，
     * 使用抖音授权填入(douyin)，
     * 使用快手授权填入(kuaishou),
     * 使用唯品会授权填入（weipinhui）,
     * 使用视频号授权填入（wechatChannels）,
     * 使用小红书授权填入(xiaohongshu)。
     * 是否必填该属性，请查看参数字典 （若通过第三方授权方式获取单号partnerId,partnerKey参数为必填,参数值可通过第三方授权接口获取)
     * 参数字典链接：https://api.kuaidi100.com/document/5f0ff6e82977d50a94e10237.html
     */
    private String net;

    /**
     * 电子面单承载编号；电子面单下单时有填写，则电子面单取消时必填
     */
    private String code;

    /**
     * 快递公司的编码，一律用小写字母，见参数字典
     * 参数字典链接：https://api.kuaidi100.com/document/5f0ff6e82977d50a94e10237.html
     */
    private String kuaidicom;

    /**
     * 快递单号
     */
    @NotBlank(message = "快递单号不能为空")
    private String kuaidinum;

    /**
     * 快递公司订单号，对应下单时返回的kdComOrderNum，如果下单时有返回该字段，则取消时必填，否则可以不填
     */
    private String orderId;

    /**
     * 取消原因
     */
    private String reason;

    /**
     * 业务员编码；电子面单下单时有填写，则电子面单取消时必填
     */
    private String checkMan;

    /**
     * 产品类型，取消京东和顺丰冷链订单必填
     */
    private String expType;
}

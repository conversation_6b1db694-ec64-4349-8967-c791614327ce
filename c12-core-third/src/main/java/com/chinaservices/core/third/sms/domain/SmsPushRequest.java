package com.chinaservices.core.third.sms.domain;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.util.Map;

@Data
public class SmsPushRequest {

    // 应用名称
    @NotBlank(message = "应用名称不能为空")
    private String appName = "a7ffac916c55457ebf6ced390b4567d6";

    // 模板ID
    @NotBlank(message = "模板ID不能为空")
    private String templateId;

    // 电话号码
    @NotEmpty(message = "模板ID不能为空")
    private String[] mobiles;

    // 原始数据
    @NotBlank(message = "原始数据不能为空")
    private String originalContent;

    // 根据模板字段信息填写，不做校验
    private Map<String,String> params;

    // 不填，填了自动切换成签名模板推送
    private String signId = "101682";
}

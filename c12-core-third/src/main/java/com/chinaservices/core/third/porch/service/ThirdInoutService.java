package com.chinaservices.core.third.porch.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.ContentType;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpStatus;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.chinaservices.core.third.annotation.EdiLogAnnotation;
import com.chinaservices.core.third.constant.ThirdResponse;
import com.chinaservices.core.third.enums.ResponseCodeEnum;
import com.chinaservices.core.third.porch.domain.*;
import com.chinaservices.core.third.porch.properties.PorchProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
public class ThirdInoutService {

    private final PorchProperties porchProperties;

    public ThirdInoutService(PorchProperties porchProperties) {
        this.porchProperties = porchProperties;
    }

    public static final int INOUT_TIME_OUT = 15000;

    /**
     * 查询进出门详情
     *
     * @param porchQueryRequest 查询进出门请求
     * @return ThirdResponse
     */
    @EdiLogAnnotation(apiDescription = "查询进出门详情", methodType = "POST")
    public ThirdResponse queryInoutInfo(PorchQueryRequest porchQueryRequest) {
        Map<String, Object> paramMap = BeanUtil.beanToMap(porchQueryRequest, false, true);
        String formData = HttpUtil.toParams(paramMap);
        log.info("查询进出门详情，请求地址：{}，request：{}", porchProperties.getQueryInoutUrl(), formData);
        HttpResponse httpResponse = null;
        try {
            httpResponse = HttpRequest.post(porchProperties.getQueryInoutUrl())
                    .contentType(ContentType.FORM_URLENCODED.getValue())
                    .body(formData)
                    .timeout(INOUT_TIME_OUT)
                    .execute();
            String responseBody = httpResponse.body();
            log.info("查询进出门详情，response：{}", responseBody);
            PorchCommonResponse response = JSONUtil.toBean(responseBody, PorchCommonResponse.class);
            if (ObjUtil.isNotNull(response) && ObjUtil.equals(HttpStatus.HTTP_OK, response.getCode())) {
                PorchResultResponse porchResultResponse = JSONUtil.toBean(response.getResult().toString(), PorchResultResponse.class);
                log.info("查询进出门详情，responseData：{}", JSONUtil.toJsonStr(porchResultResponse));
                return ThirdResponse.success(porchResultResponse, porchProperties.getQueryInoutUrl());
            } else {
                return ThirdResponse.fail(String.valueOf(response.getCode()), JSONUtil.toJsonStr(response.getMsg()), porchProperties.getQueryInoutUrl());
            }
        } catch (Exception e) {
            log.error("查询进出门详情请求异常:", e);
            return ThirdResponse.exception(ResponseCodeEnum.RESPONSE_DATA_NOT_EXISTS.getMessage(), porchProperties.getQueryInoutUrl());
        } finally {
            if (httpResponse != null) {
                httpResponse.close();
            }
        }
    }

    /**
     * 启动进出库
     *
     * @param porchStartRequest 启动进出库请求参数
     * @return ThirdResponse
     */
    @EdiLogAnnotation(apiDescription = "启动进出库", methodType = "POST")
    public ThirdResponse ableInout(PorchStartRequest porchStartRequest) {
        Map<String, Object> paramMap = BeanUtil.beanToMap(porchStartRequest, false, true);
        String formData = HttpUtil.toParams(paramMap);
        log.info("启动进出库，请求地址：{}，request：{}", porchProperties.getAbleInoutUrl(), formData);
        HttpResponse httpResponse = null;
        try {
            httpResponse = HttpRequest.post(porchProperties.getAbleInoutUrl())
                    .contentType(ContentType.FORM_URLENCODED.getValue())
                    .body(formData)
                    .timeout(INOUT_TIME_OUT)
                    .execute();
            String responseBody = httpResponse.body();
            log.info("启动进出库，response：{}", responseBody);
            PorchCommonResponse response = JSONUtil.toBean(responseBody, PorchCommonResponse.class);
            if (ObjUtil.isNotNull(response) && ObjUtil.equals(HttpStatus.HTTP_OK, response.getCode())) {
                return ThirdResponse.success(response.getResult(), porchProperties.getAbleInoutUrl());
            } else {
                return ThirdResponse.fail(String.valueOf(response.getCode()), JSONUtil.toJsonStr(response.getMsg()), porchProperties.getAbleInoutUrl());
            }
        } catch (Exception e) {
            log.error("启动进出库调用失败", e);
            return ThirdResponse.exception(JSONUtil.toJsonStr(e.getMessage()), porchProperties.getAbleInoutUrl());
        } finally {
            if (httpResponse != null) {
                httpResponse.close();
            }
        }
    }

    /**
     * 停止进出库
     *
     * @param porchStopRequest 停止进出库请求参数
     * @return ThirdResponse
     */
    @EdiLogAnnotation(apiDescription = "停止进出库", methodType = "POST")
    public ThirdResponse stopInout(PorchStopRequest porchStopRequest) {
        Map<String, Object> paramMap = BeanUtil.beanToMap(porchStopRequest, false, true);
        String formData = HttpUtil.toParams(paramMap);
        log.info("停止进出库，请求地址：{}，request：{}", porchProperties.getStopInoutUrl(), formData);
        HttpResponse httpResponse = null;
        try {
            httpResponse = HttpRequest.post(porchProperties.getStopInoutUrl())
                    .contentType(ContentType.FORM_URLENCODED.getValue())
                    .body(formData)
                    .timeout(INOUT_TIME_OUT)
                    .execute();
            String responseBody = httpResponse.body();
            log.info("停止进出库，response：{}", responseBody);
            PorchCommonResponse response = JSONUtil.toBean(responseBody, PorchCommonResponse.class);
            if (ObjUtil.isNotNull(response) && ObjUtil.equals(HttpStatus.HTTP_OK, response.getCode())) {
                return ThirdResponse.success(response.getResult(), porchProperties.getStopInoutUrl());
            } else {
                return ThirdResponse.fail(String.valueOf(response.getCode()), JSONUtil.toJsonStr(response.getMsg()), porchProperties.getStopInoutUrl());
            }
        } catch (Exception e) {
            log.error("停止进出库调用失败", e);
            return ThirdResponse.exception(JSONUtil.toJsonStr(e.getMessage()), porchProperties.getStopInoutUrl());
        } finally {
            if (httpResponse != null) {
                httpResponse.close();
            }
        }
    }

    /**
     * 查询最近一段时间的识别结果
     *
     * @param porchQueryLastRequest 请求参数
     * @return ThirdResponse
     */
    @EdiLogAnnotation(apiDescription = "查询最近一段时间的识别结果", methodType = "POST")
    public ThirdResponse queryLastInout(PorchQueryLastRequest porchQueryLastRequest) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("taskId", porchQueryLastRequest.getTaskId());
        paramMap.put("searchTime", porchQueryLastRequest.getSearchTime());
        paramMap.put("current", porchQueryLastRequest.getCurrent());
        paramMap.put("limit", porchQueryLastRequest.getLimit());
        String formData = HttpUtil.toParams(paramMap);
        log.info("查询最近一段时间的识别结果，请求地址：{}，request：{}", porchProperties.getQueryLastInoutUrl(), formData);
        HttpResponse httpResponse = null;
        try {
            httpResponse = HttpRequest.post(porchProperties.getQueryLastInoutUrl())
                    .contentType(ContentType.FORM_URLENCODED.getValue())
                    .body(formData)
                    .timeout(INOUT_TIME_OUT)
                    .execute();
            String responseBody = httpResponse.body();
            log.info("查询最近一段时间的识别结果，response：{}", responseBody);
            PorchCommonResponse response = JSONUtil.toBean(responseBody, PorchCommonResponse.class);
            if (ObjUtil.isNotNull(response) && ObjUtil.equals(HttpStatus.HTTP_OK, response.getCode())) {
                PorchPageResponse porchResultResponse = JSONUtil.toBean(response.getResult().toString(), PorchPageResponse.class);
                log.info("查询最近一段时间的识别结果，responseData：{}", JSONUtil.toJsonStr(porchResultResponse));
                return ThirdResponse.success(porchResultResponse, porchProperties.getQueryLastInoutUrl());
            } else {
                return ThirdResponse.fail(String.valueOf(response.getCode()), JSONUtil.toJsonStr(response.getMsg()), porchProperties.getQueryLastInoutUrl());
            }
        } catch (Exception e) {
            log.error("查询最近一段时间的识别结果调用失败", e);
            return ThirdResponse.exception(JSONUtil.toJsonStr(e.getMessage()), porchProperties.getQueryLastInoutUrl());
        } finally {
            if (httpResponse != null) {
                httpResponse.close();
            }
        }
    }
}

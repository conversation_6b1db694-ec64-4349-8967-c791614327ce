package com.chinaservices.core.third.porch.domain;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 任务信息类
 * @date 2025/7/4 17:02
 **/
@Data
public class PorchTaskInfoResponse {

    private Long id;
    private String name;
    private String type;
    private String location;
    private Long timerId;
    private String timerName;
    private String status;
    private int assetNum;
    private int inventoryMode;
    private double inventoryRate;
    private int inventoryTagNum;
    private String incomingAssets;
    private String outgoingAssets;
    private String paras;
    private int stoped;
    private LocalDateTime createtime;
}

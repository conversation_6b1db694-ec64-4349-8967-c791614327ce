package com.chinaservices.core.third.config;

import com.chinaservices.core.third.aspect.EdiLogAspect;
import com.chinaservices.core.third.asset.properties.AssetProperties;
import com.chinaservices.core.third.asset.service.ThirdTaskInfoService;
import com.chinaservices.core.third.onenet.service.ThirdOneNetService;
import com.chinaservices.core.third.passive.properties.PassiveProperties;
import com.chinaservices.core.third.passive.service.ThirdPassiveService;
import com.chinaservices.core.third.porch.properties.PorchProperties;
import com.chinaservices.core.third.porch.service.ThirdInoutService;
import com.chinaservices.core.third.electronicsheet.service.ThirdKd100Service;
import com.chinaservices.core.third.electronicsheet.properties.Kd100Properties;

import com.chinaservices.core.third.sms.properties.SmsProperties;
import com.chinaservices.core.third.sms.service.ThirdSmsService;
import com.chinaservices.core.third.video.service.ThirdDeviceService;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@EnableConfigurationProperties({PassiveProperties.class, SmsProperties.class, PorchProperties.class, AssetProperties.class, Kd100Properties.class})
public class ThirdCoreConfig {

    @Bean
    public ThirdDeviceService thirdDeviceService() {
        return new ThirdDeviceService();
    }

    @Bean
    public ThirdPassiveService thirdPassiveService(PassiveProperties passiveProperties) {
        return new ThirdPassiveService(passiveProperties);
    }

    @Bean
    public ThirdOneNetService thirdOneNetService() {
        return new ThirdOneNetService();
    }

    @Bean
    public ThirdSmsService thirdSmsService(SmsProperties smsProperties) {
        return new ThirdSmsService(smsProperties);
    }

    @Bean
    public ThirdInoutService thirdInoutService(PorchProperties porchProperties) {
        return new ThirdInoutService(porchProperties);
    }

    @Bean
    public ThirdTaskInfoService thirdAssetService(AssetProperties assetProperties) {
        return new ThirdTaskInfoService(assetProperties);
    }

    @Bean
    public ThirdKd100Service thirdKd100Client(Kd100Properties kd100Properties) {
        return new ThirdKd100Service(kd100Properties);
    }

    @Bean
    public EdiLogAspect ediLogAspect() {
        return new EdiLogAspect();
    }
}

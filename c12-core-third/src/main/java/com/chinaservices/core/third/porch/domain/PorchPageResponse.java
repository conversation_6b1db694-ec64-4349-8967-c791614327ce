package com.chinaservices.core.third.porch.domain;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description TODO
 * @date 2025/7/4 17:03
 **/
@Data
public class PorchPageResponse<T> {

    private List<T> records;
    private int total;
    private int size;
    private int current;
    private List<String> orders;
    private boolean optimizeCountSql;
    private boolean hitCount;
    private boolean searchCount;
    private int pages;
}

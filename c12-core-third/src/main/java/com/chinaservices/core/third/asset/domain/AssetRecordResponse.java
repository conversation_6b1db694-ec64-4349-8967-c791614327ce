package com.chinaservices.core.third.asset.domain;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class AssetRecordResponse {

    private int id;
    private int taskid;
    private String rid;
    private String qid;
    private int antennaid;
    private String epc;
    private int inventoryCounter;
    private String assetCode;
    private String assetName;
    private String assetType;
    private String position;
    private LocalDateTime datetime;
    private String inOutStatus;
}

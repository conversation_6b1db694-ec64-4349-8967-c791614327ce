package com.chinaservices.core.third.asset.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * <AUTHOR>
 */
@ConfigurationProperties(prefix = "asset")
@Data
public class AssetProperties {

    /**
     * 启动盘存任务
     */
    private String ableTaskInfoUrl;

    /**
     * 停止盘存任务
     */
    private String stopTaskInfoUrl;

    /**
     * 盘存任务和盘存结果
     */
    private String queryTaskInfoUrl;

    /**
     * 查询盘存任务中最近 10 秒标签
     */
    private String queryLastTaskInfoUrl;
}

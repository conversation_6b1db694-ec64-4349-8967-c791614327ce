package com.chinaservices.core.third.asset.domain;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class AssetQueryRequest {

    /**
     * 任务Id
     */
    @NotNull(message = "任务不能为空")
    private Integer taskId;

    /**
     * 当前页码
     */
    @NotNull(message = "当前页码不能为空")
    private Integer current;

    /**
     * 每页记录数
     */
    @NotNull(message = "每页记录数不能为空")
    private Integer limit;
}

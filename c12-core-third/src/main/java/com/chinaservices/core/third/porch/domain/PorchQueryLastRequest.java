package com.chinaservices.core.third.porch.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * . 查询最近一段时间的识别结果入参
 */
@Data
public class PorchQueryLastRequest {

    /**
     * 任务标识
     */
    @NotNull(message = "任务标识不能为空")
    private Integer taskId;

    /**
     * 具体时间
     */
    @NotNull(message = "具体时间不能为空")
    private String searchTime;

    /**
     * 当前页码
     */
    @NotNull(message = "当前页码不能为空")
    private Integer current;

    /**
     * 每页记录数
     */
    @NotNull(message = "每页记录数不能为空")
    private Integer limit;
}

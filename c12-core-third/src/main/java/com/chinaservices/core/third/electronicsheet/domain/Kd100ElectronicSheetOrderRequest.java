package com.chinaservices.core.third.electronicsheet.domain;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Data
public class Kd100ElectronicSheetOrderRequest {

    /**
     * 打印类型，
     * NON：只下单不打印（默认）；
     * IMAGE:生成图片短链；HTML:生成html短链；
     * CLOUD:使用快递100云打印机打印，使用CLOUD时siid必填
     */
    @NotBlank(message = "打印类型不能为空")
    private String printType;

    /**
     * 电子面单客户账户或月结账号，需贵司向当地快递公司网点申请（参考电子面单申请指南）； 是否必填该属性，请查看参数字典
     * 电子面单申请指南链接：https://api.kuaidi100.com/document/zhanghaoshenqingzhinan.html
     * 参数字典链接：https://api.kuaidi100.com/document/5f0ff6e82977d50a94e10237.html
     */
    @NotBlank(message = "电子面单客户账户或月结账号不能为空")
    private String partnerId;

    /**
     * 电子面单密码，需贵司向当地快递公司网点申请； 是否必填该属性，请查看参数字典
     * 参数字典链接：https://api.kuaidi100.com/document/5f0ff6e82977d50a94e10237.html
     */
    private String partnerKey;

    /**
     * 电子面单密钥，需贵司向当地快递公司网点申请； 是否必填该属性，请查看参数字典
     * 参数字典链接：https://api.kuaidi100.com/document/5f0ff6e82977d50a94e10237.html
     */
    private String partnerSecret;

    /**
     * 电子面单客户账户名称，需贵司向当地快递公司网点申请； 是否必填该属性，请查看参数字典
     * 参数字典链接：https://api.kuaidi100.com/document/5f0ff6e82977d50a94e10237.html
     */
    private String partnerName;

    /**
     * 收件网点名称,由快递公司当地网点分配，
     * 若使用淘宝授权填入（taobao），
     * 使用菜鸟授权填入（cainiao),
     * 使用京东授权填入（jdalpha),
     * 使用拼多多授权填入(pinduoduoWx)，
     * 使用抖音授权填入(douyin)，
     * 使用快手授权填入(kuaishou),
     * 使用唯品会授权填入（weipinhui）,
     * 使用视频号授权填入（wechatChannels）,
     * 使用小红书授权填入(xiaohongshu)。
     * 是否必填该属性，请查看参数字典 （若通过第三方授权方式获取单号partnerId,partnerKey参数为必填,参数值可通过第三方授权接口获取)
     * 参数字典链接：https://api.kuaidi100.com/document/5f0ff6e82977d50a94e10237.html
     */
    private String net;

    /**
     * 电子面单承载编号，需贵司向当地快递公司网点申请； 是否必填该属性，请查看参数字典
     * 参数字典链接：https://api.kuaidi100.com/document/5f0ff6e82977d50a94e10237.html
     */
    private String code;

    /**
     * 电子面单承载快递员名，需贵司向当地快递公司网点申请； 是否必填该属性，请查看参数字典
     * 参数字典链接：https://api.kuaidi100.com/document/5f0ff6e82977d50a94e10237.html
     */
    private String checkMan;

    /**
     * 在使用菜鸟/淘宝/拼多多授权电子面单时，若月结账号下存在多个网点，则tbNet="网点名称,网点编号" ，注意此处为英文逗号
     */
    private String tbNet;

    /**
     * 快递公司的编码，一律用小写字母，请查看参数字典
     * 参数字典链接：https://api.kuaidi100.com/document/5f0ff6e82977d50a94e10237.html
     */
    @NotBlank(message = "快递公司的编码不能为空")
    private String kuaidicom;

    /**
     * 收件人信息
     */
    private Kd100ElectronicSheetManRequest recMan;

    /**
     * 寄件人信息
     */
    private Kd100ElectronicSheetManRequest sendMan;

    /**
     * 物品名称,例：文件
     */
    @NotBlank(message = "物品名称不能为空")
    private String cargo;

    /**
     * 包裹总数量。该属性与子单有关，
     * 如果需要子单（指同一个订单打印出多张电子面单，即同一个订单返回多个面单号），needChild = 1、count 需要大于1，
     * 如count = 2 则一个主单 一个子单，count = 3则一个主单 二个子单；返回的子单号码见返回结果的childNum字段
     */
    @NotBlank(message = "包裹总数量不能为空")
    private int count;

    /**
     * 物品总重量KG，例：1.5，单位kg。极兔速递必填，其他快递公司非必填
     */
    private Double weight;

    /**
     * 支付方式：
     * SHIPPER：寄方付（默认）
     * CONSIGNEE：到付
     * MONTHLY：月结
     * THIRDPARTY：第三方支付
     * （详细请查看参数字典 ）
     * 参数字典链接：https://api.kuaidi100.com/document/5f0ff6e82977d50a94e10237.html
     */
    private String payType;

    /**
     * 产品类型： 如
     * 标准快递（默认）
     * 顺丰标快（陆运）
     * EMS经济
     * （详细请请查看参数字典 ）
     * 参数字典链接：https://api.kuaidi100.com/document/5f0ff6e82977d50a94e10237.html
     */
    private String expType;

    /**
     * 备注
     */
    private String remark;

    /**
     * 打印设备，通过打印机输出的设备码进行获取，printType为CLOUD时必填
     */
    private String siid;

    /**
     * 打印方向，
     * 0：正方向（默认）；
     * 1：反方向；
     * 只有printType为CLOUD时该参数生效
     */
    private String direction;

    /**
     * 主单模板，通过管理后台的快递公司模板V2信息获取，当printType为IMAGE，HTML，CLOUD时必填（仅限于网点面单，电商平台面单不需要传该参数）
     * 快递公司模板V2链接：https://api.kuaidi100.com/manager/v2/shipping-label/template-shipping-label
     */
    private String tempId;

    /**
     * 子单模板，部分快递公司需指定。通过管理后台的快递公司模板V2信息获取
     * 快递公司模板V2链接：https://api.kuaidi100.com/manager/v2/shipping-label/template-shipping-label
     */
    private String childTempId;

    /**
     * 回单模板，部分快递公司需指定。通过管理后台的快递公司模板V2信息获取
     * 快递公司模板V2链接：https://api.kuaidi100.com/manager/v2/shipping-label/template-shipping-label
     */
    private String backTempId;
    /**
     * 贵司内部自定义的订单编号,需要保证唯一性，非必填
     */
    private String orderId;
    /**
     * 是否重新下单，默认true，配合orderId使用；当orderId不为空，reorder设置为false，
     * 48小时内只会返回第一次下单成功的内容（返回信息代码为30011）;否则每次下单都认为重新下发订单
     */
    private boolean reorder;
}

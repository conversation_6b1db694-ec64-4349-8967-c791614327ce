package com.chinaservices.core.third.asset.domain;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Data
public class AssetStartRequest {

    /**
     * 任务名称
     */
    @NotBlank(message = "任务名称不能为空")
    private String taskname;

    /**
     * 设备位置
     */
    @NotBlank(message = "任务位置不能为空")
    private String location;

    /**
     * 盘存任务的类型
     * boolean 为 true 时，表示循环盘。盘存任务不会自动停止，需要用户手动调用停止接口；
     * boolean 为 false 时，表示快速盘。不再盘存到新的标签超过 30 秒后，将自动停止
     */
    @NotBlank(message = "盘存任务的类型不能为空")
    private String reinventory;
}

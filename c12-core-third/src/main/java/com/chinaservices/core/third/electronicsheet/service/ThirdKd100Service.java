package com.chinaservices.core.third.electronicsheet.service;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.HexUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.http.ContentType;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONUtil;
import com.chinaservices.core.third.annotation.EdiLogAnnotation;
import com.chinaservices.core.third.constant.ThirdResponse;
import com.chinaservices.core.third.enums.ResponseCodeEnum;
import com.chinaservices.core.third.electronicsheet.domain.*;
import com.chinaservices.core.third.electronicsheet.properties.Kd100Properties;
import com.chinaservices.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 快递 100 客户端
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class ThirdKd100Service {

    private final Kd100Properties kd100Properties;

    public ThirdKd100Service(Kd100Properties kd100Properties) {
        this.kd100Properties = kd100Properties;
    }

    /**
     * 构建请求参数（body）
     *
     * method：业务类型（order，cancel）
     * key：授权码，请到快递100页面申请企业版接口获取
     * sign：32位大写，签名，用于验证身份，按MD5 (param +t+key+ secret)的顺序进行MD5加密，不需要加上“+”号，secret在企业管理后台获取
     * t：时间戳如：1576123932000
     * param：由其他字段拼接
     *
     * @return String 请求参数
     */
    private Map<String, Object> buildRequestBody(String method, Object paramObj) {
        String param = JsonUtils.toJsonString(paramObj);
        String timestamp = String.valueOf(System.currentTimeMillis() / 1000);
        String signFormat = StrUtil.format("{}{}{}{}", param, timestamp, kd100Properties.getKey(), kd100Properties.getSecret());
        String sign = HexUtil.encodeHexStr(DigestUtil.md5(signFormat), Boolean.FALSE);
        Map<String, Object> paramMap = MapUtil.newHashMap();
        paramMap.put("key", kd100Properties.getKey());
        paramMap.put("sign", sign);
        paramMap.put("t", timestamp);
        paramMap.put("param", param);
        paramMap.put("method", method);
        return paramMap;
    }

    /**
     * 电子面单下单
     *
     * @param kd100ElectronicSheetOrderRequest 电子面单下单请求参数
     * @return Kd100ElectronicSheetOrderDataResponse 电子面单下单返回响应
     */
    @EdiLogAnnotation(apiDescription = "电子面单下单", methodType = "POST")
    public ThirdResponse courier(Kd100ElectronicSheetOrderRequest kd100ElectronicSheetOrderRequest) {
        Map<String, Object> paramMap = this.buildRequestBody("order", kd100ElectronicSheetOrderRequest);
        log.info("电子面单下单，请求地址：{}，request：{}", kd100Properties.getOrderUrl(), paramMap);
        HttpResponse httpResponse = null;
        try {
            httpResponse = HttpRequest.post(kd100Properties.getOrderUrl())
                    .contentType(ContentType.FORM_URLENCODED.getValue())
                    .form(paramMap)
                    .timeout(15000)
                    .execute();
            String responseBody = httpResponse.body();
            log.info("电子面单下单，response：{}", responseBody);
            Kd100ElectronicSheetOrderResponse response = JSONUtil.toBean(responseBody, Kd100ElectronicSheetOrderResponse.class);
            if (StrUtil.equals("true", response.getSuccess()) && ObjUtil.equals(200, response.getCode())) {
                Kd100ElectronicSheetOrderDataResponse kd100ElectronicSheetOrderDataResponse = response.getData();
                log.info("电子面单下单，responseData：{}", JSONUtil.toJsonStr(kd100ElectronicSheetOrderDataResponse));
                return ThirdResponse.success(kd100ElectronicSheetOrderDataResponse, kd100Properties.getOrderUrl());
            } else {
                return ThirdResponse.fail(String.valueOf(response.getCode()), JSONUtil.toJsonStr(response.getMessage()), kd100Properties.getOrderUrl());
            }
        } catch (Exception e) {
            log.error("电子面单下单:", e);
            return ThirdResponse.exception(ResponseCodeEnum.RESPONSE_DATA_NOT_EXISTS.getMessage(), kd100Properties.getOrderUrl());
        } finally {
            if (httpResponse != null) {
                httpResponse.close();
            }
        }
    }

    /**
     * 电子面单取消
     *
     * @param kd100ElectronicSheetCancelRequest 电子面单取消取消请求参数
     * @return Kd100ElectronicSheetCancelRequest 电子面单取消取消返回响应
     */
    @EdiLogAnnotation(apiDescription = "电子面单取消", methodType = "POST")
    public ThirdResponse unCourier(Kd100ElectronicSheetCancelRequest kd100ElectronicSheetCancelRequest) {
        Map<String, Object> paramMap = this.buildRequestBody("cancel", kd100ElectronicSheetCancelRequest);
        log.info("电子面单取消，请求地址：{}，request：{}", kd100Properties.getOrderUrl(), paramMap);
        HttpResponse httpResponse = null;
        try {
            httpResponse = HttpRequest.post(kd100Properties.getOrderUrl())
                    .contentType(ContentType.FORM_URLENCODED.getValue())
                    .form(paramMap)
                    .timeout(15000)
                    .execute();
            String responseBody = httpResponse.body();
            log.info("电子面单取消，response：{}", responseBody);
            Kd100ElectronicSheetCancelResponse response = JSONUtil.toBean(responseBody, Kd100ElectronicSheetCancelResponse.class);
            if (StrUtil.equals("true", response.getSuccess()) && ObjUtil.equals(200, response.getCode())) {
                return ThirdResponse.success(response.getMessage(), kd100Properties.getOrderUrl());
            } else {
                return ThirdResponse.fail(String.valueOf(response.getCode()), JSONUtil.toJsonStr(response.getMessage()), kd100Properties.getOrderUrl());
            }
        } catch (Exception e) {
            log.error("电子面单取消:", e);
            return ThirdResponse.exception(ResponseCodeEnum.RESPONSE_DATA_NOT_EXISTS.getMessage(), kd100Properties.getOrderUrl());
        } finally {
            if (httpResponse != null) {
                httpResponse.close();
            }
        }
    }
}

package com.chinaservices.core.exception;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum GlobalExceptionEnum implements BizError {

    SUCCESS(200, "成功"),

    SYSTEM_ERROR(10000, "系统异常"),
    PARAM_ERROR(10001, "缺少必要参数"),
    ID_NOT_EMPTY(10002, "ID不能为空"),
    IDS_NOT_EMPTY(10003, "IDS不能为空"),
    START_TIME_LT_END_TIME(10004, "开始时间不能大于结束时间"),
    SAVE_ERROR(10005, "保存失败"),
    DELETE_FAILED(10006, "删除失败"),
    DELETE_REPEATEDLY(10007, "请勿重复删除"),
    CODE_EMPTY(10008, "%s代码不能为空"),
    CODE_EXIST(10009, "%s代码：【%s】已存在"),
    NAME_EXIST(10010, "%s名称：【%s】已存在"),
    NO_EXIST(10011, "%s单号：【%s】已存在"),
    NO_NOT_EXIST(10012, "%s单号：【%s】不存在"),
    ABLE_EXIST(10013, "存在%s的%s：【%s】"),
    BARCODE_GENERATION_FAILED(10014, "条形码生成失败"),
    BARCODE_EMPTY(10015, "条形码不存在"),
    QUERY_USER_EMPTY(10016, "查询用户为空"),
    ;

    private final Integer code;
    private final String msg;
}
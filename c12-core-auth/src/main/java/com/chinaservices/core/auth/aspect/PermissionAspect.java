package com.chinaservices.core.auth.aspect;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaMode;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.ObjUtil;
import com.chinaservices.session.constant.DeviceType;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

@Aspect
@Component
public class PermissionAspect {

    @Around("@annotation(saCheckPermission)")
    public Object checkPermission(ProceedingJoinPoint joinPoint, SaCheckPermission saCheckPermission) throws Throwable {
        if (ObjUtil.equals(DeviceType.PC.getDevice(), StpUtil.getLoginDevice())) {
            this.checkPermissions(saCheckPermission);
        }
        return joinPoint.proceed();
    }

    private void checkPermissions(SaCheckPermission saCheckPermission) {
        String[] permissions = saCheckPermission.value();
        if (permissions == null || permissions.length == 0) {
            return;
        }
        SaMode mode = this.getAnnotationMode(saCheckPermission);
        if (mode == SaMode.OR) {
            StpUtil.checkPermissionOr(permissions);
        } else {
            StpUtil.checkPermissionAnd(permissions);
        }
    }

    private SaMode getAnnotationMode(SaCheckPermission annotation) {
        try {
            Method modeMethod = annotation.getClass().getMethod("mode");
            return (SaMode) modeMethod.invoke(annotation);
        } catch (Exception e) {
            return SaMode.AND;
        }
    }
}
package com.chinaservices.core.jpa;

import cn.hutool.core.util.*;
import com.chinaservices.config.LogicDeletePropertiesUtil;
import com.chinaservices.config.SqlAutoConfig;
import com.chinaservices.config.properties.SqlProperties;
import com.chinaservices.jpa.CommonJpaRepository;
import com.chinaservices.jpa.dialect.Dialect;
import com.chinaservices.jpa.dialect.MysqlDialect;
import com.chinaservices.jpa.dialect.OracleDialect;
import com.chinaservices.jpa.util.DaoUtils;
import com.chinaservices.jpa.util.EntityManagerUtils;
import com.chinaservices.sql.SqlExecutor;
import jakarta.persistence.EntityManager;
import jakarta.persistence.EntityManagerFactory;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.boot.model.naming.CamelCaseToUnderscoresNamingStrategy;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;
import org.springframework.context.annotation.Primary;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;
import static com.chinaservices.core.constant.JpaConstant.DATA_DELETE_STRING;
import static com.chinaservices.core.constant.JpaConstant.DATA_SCOPE_STRING;
import static org.hibernate.cfg.JdbcSettings.STATEMENT_INSPECTOR;

/**
 * <AUTHOR>
 */
@Configuration
@EnableTransactionManagement(proxyTargetClass = true)
@Slf4j
public class JpaConfig {

    @Bean
    @Primary
    public LocalContainerEntityManagerFactoryBean entityManagerFactory(EntityManagerFactoryBuilder builder, DataSource dataSource) {
        LocalContainerEntityManagerFactoryBean factoryBean = builder.dataSource(dataSource).packages("com.chinaservices.wms", "com.chinaservices.auth", "com.chinaservices.core", "com.chinaservices.export", "com.chinaservices.admin", "com.chinaservices.print", "com.chinaservices.data", "com.chinaservices.ai").build();
        Map<String, Object> properties = new HashMap<>();
        properties.put("hibernate.physical_naming_strategy", new CamelCaseToUnderscoresNamingStrategy()); // 驼峰转下划线
        properties.put("hibernate.format_sql", "false");  // 格式化 SQL
        properties.put("hibernate.show_sql", "false");    // 显示 SQL
        properties.put("hibernate.use_sql_comments", false); // 使用 SQL 注释
        properties.put("hibernate.cache.use_second_level_cache", false);
        properties.put("hibernate.cache.use_query_cache", false);
        if (ClassLoaderUtil.isPresent(DATA_SCOPE_STRING)) {
            properties.put(STATEMENT_INSPECTOR, DATA_SCOPE_STRING);
        } else {
            properties.put(STATEMENT_INSPECTOR, DATA_DELETE_STRING);
        }
        factoryBean.setJpaPropertyMap(properties);
        HibernateJpaVendorAdapter hibernateJpaVendorAdapter = new HibernateJpaVendorAdapter();
        hibernateJpaVendorAdapter.setGenerateDdl(false);
        hibernateJpaVendorAdapter.setShowSql(false);
        factoryBean.setJpaVendorAdapter(hibernateJpaVendorAdapter); // 使用Hibernate作为JPA提供者
        factoryBean.afterPropertiesSet();
        return factoryBean;
    }


    @Bean
    public LogicDeletePropertiesUtil logicDeletePropertiesUtil() {
        return new LogicDeletePropertiesUtil();
    }

    @Bean
    public SqlExecutor sqlExecutor() {
        return new SqlExecutor();
    }

    @Bean
    @DependsOn("daoUtils")
    public SqlAutoConfig sqlAutoConfig() {
        return new SqlAutoConfig();
    }

    @Bean
    public CommonJpaRepository commonJpaRepository(EntityManager entityManager, DaoUtils daoUtils, UpdateOrInsertDao dao) {
        return new CustomCommonJpaRepositoryImpl(entityManager, getDialect(entityManager), daoUtils, dao);
    }

    @Bean
    public UpdateOrInsertDao updateOrInsertDao(EntityManager entityManager, DaoUtils daoUtils) {
        return new UpdateOrInsertDao(entityManager, getDialect(entityManager), daoUtils);
    }

    @Bean
    public SqlProperties sqlProperties() {
        return new SqlProperties();
    }

    @Bean
    public EntityManager entityManager(LocalContainerEntityManagerFactoryBean entityManagerFactory) {
        return entityManagerFactory.getObject().createEntityManager();
    }

    @Bean
    public PlatformTransactionManager transactionManager(LocalContainerEntityManagerFactoryBean entityManagerFactory) {
        JpaTransactionManager jpaTransactionManager = new JpaTransactionManager(entityManagerFactory.getObject());
        jpaTransactionManager.setDefaultTimeout(60);
        return jpaTransactionManager;
    }

    @Bean
    public DaoUtils daoUtils(EntityManager entityManager) {
        EntityManagerUtils.setMainEntityManager(entityManager);
        EntityManagerFactory entityManagerFactory = entityManager.getEntityManagerFactory();
        DataSource ds = ReflectUtil.invoke(entityManagerFactory, "getDataSource", new Object[0]);
        if (null == ds) {
            log.warn("Unable to get dataSource");
        }
        return new DaoUtils(entityManager, ds, getDialect(entityManager));
    }

    private Dialect getDialect(EntityManager entityManager) {
        EntityManagerFactory entityManagerFactory = entityManager.getEntityManagerFactory();
        Map<String, Object> properties = entityManagerFactory.getProperties();
        Object hibernateDialect = properties.getOrDefault("hibernate.dialect", "");
        if (StrUtil.containsIgnoreCase(StrUtil.toString(hibernateDialect), "oracle")) {
            return new OracleDialect();
        }
        return new MysqlDialect();
    }
}
